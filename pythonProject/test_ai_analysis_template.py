#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI分析模板输出
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta
import numpy as np

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入修改后的类
from 测试_copy import TelecomMonitor

def create_test_data():
    """创建测试数据"""
    # 创建30天的测试数据
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # 生成随机案件数据
    np.random.seed(42)  # 固定随机种子以便重现
    telecom_cases = np.random.poisson(3, len(date_range))  # 电信案件，平均3起/天
    unicom_cases = np.random.poisson(2, len(date_range))   # 联通案件，平均2起/天
    mobile_cases = np.random.poisson(4, len(date_range))   # 移动案件，平均4起/天
    
    df = pd.DataFrame({
        '日期': date_range,
        '电信': telecom_cases,
        '联通': unicom_cases,
        '移动': mobile_cases
    })
    
    # 创建案件详情数据
    case_details = pd.DataFrame({
        'case_date': [end_date.date()] * 10,
        'suspect_phone_info': ['电信'] * 3 + ['联通'] * 3 + ['移动'] * 4,
        '简要案情': ['网络诈骗案件'] * 10,
        'involved_amount': [600000, 800000, 1200000] * 3 + [500000],
        '年龄': [25, 35, 45, 30, 40, 50, 28, 38, 48, 32],
        '案件子类': ['网络购物诈骗'] * 10
    })
    
    return df, case_details

def test_ai_analysis_template():
    """测试AI分析模板输出"""
    print("🧪 测试AI分析模板输出")
    print("=" * 50)
    
    # 创建监控实例
    monitor = TelecomMonitor()
    
    # 创建测试数据
    print("📊 创建测试数据...")
    df, case_details = create_test_data()
    print(f"✅ 测试数据创建完成: {len(df)}天数据, {len(case_details)}条案件详情")
    
    # 测试分析数据准备
    print("\n📋 测试分析数据准备...")
    analysis_data = monitor._prepare_analysis_data(df, case_details)
    print(f"✅ 分析数据准备完成，包含 {len(analysis_data)} 个数据项")
    
    # 显示部分分析数据
    print("\n📈 分析数据概览:")
    print(f"  总案件数: {analysis_data['total_cases']}")
    print(f"  日均案件: {analysis_data['daily_avg']:.1f}")
    print(f"  分析周期: {analysis_data['analysis_start_date']} - {analysis_data['analysis_end_date']}")
    
    # 测试提示词生成
    print("\n📝 测试提示词生成...")
    for analysis_key in ['trend_analysis', 'case_pattern_analysis', 'comprehensive_analysis']:
        prompt = monitor._generate_analysis_prompt(analysis_key, analysis_data)
        analysis_name = {
            'trend_analysis': '趋势分析',
            'case_pattern_analysis': '案情特征分析',
            'comprehensive_analysis': '综合分析'
        }[analysis_key]
        print(f"  {analysis_name}提示词长度: {len(prompt)} 字符")
    
    # 测试模拟AI分析结果显示
    print("\n🤖 测试模拟AI分析结果显示...")
    mock_results = {
        'trend_analysis': '基于数据分析，近期电信诈骗案件呈现以下趋势：1. 案件总量保持稳定，日均3起左右；2. 周环比变化较小；3. 整体风险可控。',
        'case_pattern_analysis': '运营商对比分析显示：1. 电信用户案件主要集中在网络购物诈骗；2. 联通用户受害年龄偏大；3. 移动用户案件数量最多但单案金额相对较小。',
        'comprehensive_analysis': '综合分析表明：1. 当前诈骗态势总体平稳；2. 各运营商用户风险特征明显；3. 建议加强针对性防护措施。'
    }
    
    monitor.display_analysis_results(mock_results, analysis_data)
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    test_ai_analysis_template()
