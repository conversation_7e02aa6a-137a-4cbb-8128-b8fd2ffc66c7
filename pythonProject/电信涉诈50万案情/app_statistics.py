from util import query_sql, engine, text
import re
from collections import Counter
import logging


def app_name_statistics(date_query: str):
    """
    统计指定日期的应用名称，并将新的应用名称插入到数据库中

    Args:
        date_query (str): 查询日期，格式：'YYYY-MM-DD'

    Returns:
        list: 包含新应用名称和数量的字典列表
    """
    # 输入验证
    if not date_query or not isinstance(date_query, str):
        raise ValueError("date_query must be a non-empty string")

    # 验证日期格式（简单验证）
    if not re.match(r'^\d{4}-\d{2}-\d{2}$', date_query):
        raise ValueError("date_query must be in format 'YYYY-MM-DD'")

    try:
        # 查询反欺诈案例数据
        my_sql = f'''
            select *
            from anti_fraud_case_new afcnt 
            where insert_day = '{date_query}'
        '''
        data_list = query_sql(my_sql)

        if not data_list:
            logging.info(f"No data found for date: {date_query}")
            return []

        # 提取和清理应用名称
        app_list = []
        for dl in data_list:
            app_name = str(dl.get('app_name', ''))  # 使用get方法避免KeyError

            # 如果app_name为None或'None'，跳过
            if not app_name or app_name.lower() == 'none':
                continue

            # 移除所有空白字符
            app_name = re.sub(r"\s+", "", app_name)

            # 按逗号分割
            split_names = app_name.split(',')

            for name in split_names:
                # 去除前后空白并检查是否为空
                name = name.strip()
                if name and name.lower() != 'none':  # 过滤空字符串和'none'
                    app_list.append(name)

        if not app_list:
            logging.info("No valid app names found after processing")
            return []

        # 统计应用名称出现次数
        counts = Counter(app_list)
        result = []

        # 处理每个唯一的应用名称
        for app_name, count in counts.items():
            try:
                # 检查应用名称是否已存在
                app_sql = f'''
                    select app_name, count(1) num
                    from anti_app_name aan 
                    where app_name = '{app_name}'
                '''
                app_num = query_sql(app_sql)

                # 如果应用名称不存在，插入新记录
                if app_num and app_num[0]['num'] == 0:
                    in_sql = f'''
                        insert into anti_app_name (app_name, create_time) values('{app_name}', now())
                    '''
                    with engine.connect() as connection:
                        connection.execute(text(in_sql))
                        connection.commit()

                    result.append({'app_name': app_name, 'num': count})
                    logging.info(f"Inserted new app: {app_name} with count: {count}")

            except Exception as e:
                logging.error(f"Error processing app_name '{app_name}': {str(e)}")
                continue  # 继续处理其他应用名称

        return result

    except Exception as e:
        logging.error(f"Error in app_name_statistics for date {date_query}: {str(e)}")
        raise  # 重新抛出异常以便调用者处理


def batch_app_name_statistics(date_list):
    """
    批量处理多个日期的应用名称统计

    Args:
        date_list (list): 日期列表

    Returns:
        dict: 以日期为键的结果字典
    """
    results = {}
    for date in date_list:
        try:
            results[date] = app_name_statistics(date)
        except Exception as e:
            logging.error(f"Failed to process date {date}: {str(e)}")
            results[date] = []
    return results


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    try:
        result = app_name_statistics('2025-05-31')
        print(f"Processing completed. Found {len(result)} new apps.")
        for item in result:
            print(f"App: {item['app_name']}, Count: {item['num']}")
    except Exception as e:
        print(f"Error: {str(e)}")