from openpyxl import Workbook
from util import file_path, provinces, query_sql, date_query
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def province_excel():
    """
    筛选涉案金额大于等于50万且发生地区有效的反欺诈案件，并导出到Excel文件
    """
    logger.info('筛选开始')

    # 确保目录存在
    os.makedirs(file_path, exist_ok=True)
    file_filters = os.path.join(file_path, "全国涉案警情案发地区.xlsx")

    my_sql = f'''
        select *
        from anti_fraud_case_new afcnt 
        where involved_amount >= 500000 and insert_day = '{date_query}'
    '''

    try:
        sql_results = query_sql(my_sql)
        if not sql_results:
            logger.warning('查询结果为空')
            return

        # 筛选有效地区的案件
        valid_cases = [
            case for case in sql_results
            if is_valid_address(case.get('occurrence_area'))
        ]

        if valid_cases:
            create_excel_file(valid_cases, file_filters)
            logger.info(f'筛选完成，共筛选出 {len(valid_cases)} 条有效记录')
        else:
            logger.warning('没有符合条件的有效记录')

    except Exception as e:
        logger.error(f'筛选过程中发生错误: {str(e)}')
        raise

    logger.info('筛选结束')


def is_valid_address(address):
    """
    验证发案地区是否有效

    Args:
        address: 发案地区字符串

    Returns:
        bool: 地区是否有效
    """
    if not address or not isinstance(address, str):
        return False

    # 去除前后空格后再验证
    address = address.strip()
    return address in provinces


def create_excel_file(data, file_name):
    """
    生成Excel文件

    Args:
        data (list): 数据集合，每个元素应为字典
        file_name (str): 文件存放路径+文件名称
    """
    if not data:
        logger.warning('数据为空，不创建Excel文件')
        return

    try:
        wb = Workbook()
        ws = wb.active

        # 写入表头（字典的 keys）
        headers = list(data[0].keys())
        ws.append(headers)

        # 写入数据行
        for row_data in data:
            # 处理可能的None值
            row_values = [
                value if value is not None else ''
                for value in row_data.values()
            ]
            ws.append(row_values)

        wb.save(file_name)
        logger.info(f'Excel文件已保存: {file_name}')

    except Exception as e:
        logger.error(f'创建Excel文件时发生错误: {str(e)}')
        raise


if __name__ == "__main__":
    try:
        province_excel()
    except Exception as e:
        logger.error(f'程序执行失败: {str(e)}')