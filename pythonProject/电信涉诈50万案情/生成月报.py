from util import engine, text, file_path, operator_key, date_range, word_name, excel_statistics_name
from word_report_utils_month import WordReportUtilsMonth
from excel_statistics import set_excel_data, group_excel_num
from re_106_95_96 import get_106_df
import logging
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def get_data():
    """
    生成案情分析月报的主函数
    """
    try:
        logger.info("文档生成开始")

        # 生成的word的文档存放地址
        word_file = os.path.join(file_path, "案情分析月报.docx")
        excel_file = os.path.join(file_path, excel_statistics_name)

        # 检查必要文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"输出目录不存在: {file_path}")
            raise FileNotFoundError(f"输出目录不存在: {file_path}")

        if not os.path.exists(excel_file):
            logger.error(f"Excel统计文件不存在: {excel_file}")
            raise FileNotFoundError(f"Excel统计文件不存在: {excel_file}")

        # 获取运营商占比
        excel_data = set_excel_data(excel_file)
        if not excel_data:
            logger.error("Excel数据为空")
            raise ValueError("Excel数据为空")

        datas = group_excel_num(excel_data, operator_key, len(excel_data))

        # 构建运营商数据字典
        data_amount = {}
        for das in datas:
            if operator_key in das:
                data_amount[das[operator_key]] = das

        # 验证必要的运营商数据是否存在
        required_operators = ['电信', '联通', '移动', '广电', '虚商']
        for operator in required_operators:
            if operator not in data_amount:
                logger.warning(f"缺少运营商数据: {operator}")
                # 提供默认值
                data_amount[operator] = {'号码': '', '总数': 0, '占比': 0}

        # 获取各运营商号码正则表达式
        phone = data_amount['电信'].get('号码', '')
        lt_phone = data_amount['联通'].get('号码', '')
        yd_phone = data_amount['移动'].get('号码', '')
        gd_phone = data_amount['广电'].get('号码', '')
        xs_phone = data_amount['虚商'].get('号码', '')

        # 构建查询语句
        queries = build_main_queries(phone, lt_phone, yd_phone, gd_phone, xs_phone)
        text_queries = build_text_queries(phone, lt_phone, yd_phone, gd_phone, xs_phone)
        hb_queries = build_hb_queries()

        # 执行查询
        results = execute_queries(queries)
        text_results = execute_queries(text_queries)
        hb_results = execute_queries(hb_queries)

        # 验证查询结果
        if not validate_query_results(results, text_results):
            logger.error("查询结果验证失败")
            raise ValueError("查询结果验证失败")

        # 处理统计数据
        top_5 = results[5][:5] if len(results) > 5 and results[5] else []
        result_str = "、".join([f"{item.get('type', 'unknown')}占比{item.get('percentage', 0)}%" for item in top_5])

        app_top_5 = results[6][:5] if len(results) > 6 and results[6] else []
        app_result_str = "、".join([f"{item.get('app_name', 'unknown')}" for item in app_top_5])

        # 获取106相关数据
        try:
            df_106 = get_106_df()
        except Exception as e:
            logger.warning(f"获取106数据失败: {e}")
            df_106 = 0

        # 构建报告数据
        report_data = build_report_data(
            results, text_results, data_amount, excel_data,
            result_str, app_result_str, df_106
        )

        # 生成Word报告
        file_stream = WordReportUtilsMonth.generate_case_analysis_report(report_data, word_file)

        logger.info("文档生成结束")
        return file_stream

    except Exception as e:
        logger.error(f"生成报告时发生错误: {e}")
        raise


def build_main_queries(phone, lt_phone, yd_phone, gd_phone, xs_phone):
    """
    构建主要查询语句
    """
    return [
        # 0 - 总体案件统计
        text(
            " select count(*) as case_count,ROUND(SUM(involved_amount)/100000000,2) as total_amout from anti_fraud_case_new_2025_temp where    DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')"),
        # 1 - URL数据统计
        text(
            " SELECT COUNT(*) as total_count, ROUND(COUNT(*)/ (select count(1) from anti_fraud_case_new_2025_temp ) *100,2) as percentage FROM anti_fraud_case_new_2025_temp  where url!='无' and DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')"),
        # 2 - APP数据统计
        text(
            " select COUNT(*) as total_count, ROUND(COUNT(*)/  (select count(1) from anti_fraud_case_new_2025_temp ) *100,2) as percentage from anti_fraud_case_new_2025_temp where app_name!='无' and app_name!='' and DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') "),
        # 3 - SMS数据统计
        text(
            " SELECT  COUNT(*) as total_count,ROUND(COUNT(*)/  (select count(1) from anti_fraud_case_new_2025_temp ) *100,2) as percentage  FROM anti_fraud_case_new_2025_temp  where connection_relationship!='无' or suspect_phone_number !='' and DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')"),
        # 4 - 国际号码统计
        text(
            " select count(*) as tel_count from anti_fraud_case_new_2025_temp where suspect_phone_number!='' and  suspect_phone_number REGEXP '^(00|\\\+)' AND suspect_phone_number NOT REGEXP '^(0086|\\\+86)' and DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') "),
        # 5 - 案件类型排行
        text(
            "SELECT @row_number:=@row_number + 1 AS id,sorted.* FROM (SELECT  case_main_type as type,COUNT(*) as total_count,ROUND(SUM(involved_amount)/10000,2) as amount, ROUND(COUNT(*)/ ( SELECT COUNT(*) as total_count  FROM anti_fraud_case_new_2025_temp where DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') ) *100,2) as percentage FROM anti_fraud_case_new_2025_temp where   DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') GROUP BY case_main_type ORDER BY total_count DESC ) AS sorted,(SELECT @row_number:=0) AS init "),
        # 6 - APP排行
        text(
            "SELECT @row_number:=@row_number + 1 AS id,  sorted.* FROM (   SELECT   app_name, COUNT(*) as total_count, ROUND(COUNT(*)/(select COUNT(*) as total_count from anti_fraud_case_new_2025_temp where app_name!='无' and app_name!='' and DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')) *100,2) as percentage FROM anti_fraud_case_new_2025_temp  where app_name not in ('','-','无','韩文','符号','未命名','日文') and DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')  GROUP BY app_name ORDER BY total_count DESC limit 20  ) AS sorted,(SELECT @row_number:=0) AS init"),
        # 7-9 电信相关查询
        *build_operator_amount_queries(phone, "电信"),
        # 10-12 联通相关查询
        *build_operator_amount_queries(lt_phone, "联通"),
        # 13-15 移动相关查询
        *build_operator_amount_queries(yd_phone, "移动"),
        # 16-18 广电相关查询
        *build_operator_amount_queries(gd_phone, "广电"),
        # 19-21 虚商相关查询
        *build_operator_amount_queries(xs_phone, "虚商"),
        # 22-26 各运营商总金额查询
        *build_operator_total_amount_queries(phone, lt_phone, yd_phone, gd_phone, xs_phone),
    ]


def build_operator_amount_queries(operator_phone, operator_name):
    """
    构建运营商金额相关查询
    """
    return [
        text(
            f"select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new_2025_temp where involved_amount>=500000 and involved_amount<1000000 and DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') and suspect_phone_number REGEXP'{operator_phone}'"),
        text(
            f"select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new_2025_temp where involved_amount>=1000000 and involved_amount<10000000 and DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') and suspect_phone_number REGEXP'{operator_phone}'"),
        text(
            f"select count(*) as total_count,sum(involved_amount) from anti_fraud_case_new_2025_temp where involved_amount>=10000000 and DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') and suspect_phone_number REGEXP'{operator_phone}'"),
    ]


def build_operator_total_amount_queries(phone, lt_phone, yd_phone, gd_phone, xs_phone):
    """
    构建运营商总金额查询
    """
    return [
        text(
            f"select IFNULL(ROUND(sum( involved_amount ) /10000,2),0) as total_money from anti_fraud_case_new_2025_temp where DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') and suspect_phone_number REGEXP'{phone}'"),
        text(
            f"select IFNULL(ROUND(sum( involved_amount ) /10000,2),0) as total_money from anti_fraud_case_new_2025_temp where DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') and suspect_phone_number REGEXP'{lt_phone}'"),
        text(
            f"select IFNULL(ROUND(sum( involved_amount ) /10000,2),0) as total_money from anti_fraud_case_new_2025_temp where DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') and suspect_phone_number REGEXP'{yd_phone}'"),
        text(
            f"select IFNULL(ROUND(sum( involved_amount ) /10000,2),0) as total_money from anti_fraud_case_new_2025_temp where DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') and suspect_phone_number REGEXP'{gd_phone}'"),
        text(
            f"select IFNULL(ROUND(sum( involved_amount ) /10000,2),0) as total_money from anti_fraud_case_new_2025_temp where DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') and suspect_phone_number REGEXP'{xs_phone}'"),
    ]


def build_text_queries(phone, lt_phone, yd_phone, gd_phone, xs_phone):
    """
    构建文本查询语句
    """
    queries = []
    operators = [
        ("电信", phone),
        ("联通", lt_phone),
        ("移动", yd_phone),
        ("广电", gd_phone),
        ("虚商", xs_phone)
    ]

    for operator_name, operator_phone in operators:
        queries.extend([
            text(
                f"select suspect_phone_info from anti_fraud_case_new_2025_temp where involved_amount>=500000 and involved_amount<1000000 and DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') and suspect_phone_number REGEXP'{operator_phone}'"),
            text(
                f"select suspect_phone_info from anti_fraud_case_new_2025_temp where involved_amount>=1000000 and involved_amount<10000000 and DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') and suspect_phone_number REGEXP'{operator_phone}'"),
            text(
                f"select suspect_phone_info from anti_fraud_case_new_2025_temp where involved_amount>=10000000 and DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') and suspect_phone_number REGEXP'{operator_phone}'"),
        ])

    # 添加高金额案件查询
    queries.append(
        text(
            "select case_number from anti_fraud_case_new_2025_temp where involved_amount>=10000000 and DATE_FORMAT(STR_TO_DATE(entry_start_time, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') ")
    )

    return queries


def build_hb_queries():
    """
    构建环比查询语句
    """
    return [
        text(
            "select count(*) as case_count from anti_fraud_case_new_2025_temp where STR_TO_DATE(insert_day, '%Y-%m-%d') = DATE_FORMAT(CURDATE()-1, '%Y-%m-%d')"),
        text(
            "select count(*) as case_count from anti_fraud_case_new_2025_temp where STR_TO_DATE(insert_day, '%Y-%m-%d') = DATE_FORMAT(CURDATE()-2, '%Y-%m-%d')")
    ]


def validate_query_results(results, text_results):
    """
    验证查询结果的有效性
    """
    if not results or len(results) < 27:
        logger.error("主查询结果数量不足")
        return False

    if not text_results or len(text_results) < 16:
        logger.error("文本查询结果数量不足")
        return False

    # 检查关键结果是否为空
    critical_indices = [0, 1, 2, 3, 4, 5, 6]
    for idx in critical_indices:
        if not results[idx] or not results[idx][0]:
            logger.error(f"关键查询结果为空: index {idx}")
            return False

    return True


def build_report_data(results, text_results, data_amount, excel_data, result_str, app_result_str, df_106):
    """
    构建报告数据字典
    """
    return {
        "date_range": date_range,
        "case_count": safe_get_value(results, 0, "case_count", "0"),
        "url_count": safe_get_value(results, 1, "total_count", "0"),
        "url_prop": safe_get_value(results, 1, "percentage", "0"),
        "app_count": safe_get_value(results, 2, "total_count", "0"),
        "app_prop": safe_get_value(results, 2, "percentage", "0"),
        "sms_count": safe_get_value(results, 3, "total_count", "0"),
        "sms_prop": safe_get_value(results, 3, "percentage", "0"),
        "telephone_count": str(data_amount.get('电信', {}).get('总数', 0)),
        "intel_count": safe_get_value(results, 4, "tel_count", "0"),
        "international_count": str(df_106),
        "international_prop": "XX",
        "case_distribution_desc": result_str,
        "case_types": results[5] if len(results) > 5 else [],
        "app_case_desc": app_result_str,
        "app_top_list": results[6] if len(results) > 6 else [],
        "loss_amount": safe_get_value(results, 0, "total_amout", "0"),

        # 电信相关数据
        "telephone_amount": safe_get_value(results, 22, "total_money", "0"),
        "high_amount_5": safe_get_value(results, 7, "total_count", "0"),
        "high_amount_10": safe_get_value(results, 8, "total_count", "0"),
        "high_amount_100": safe_get_value(results, 9, "total_count", "0"),

        # 联通相关数据
        "lt_telephone_amount": safe_get_value(results, 23, "total_money", "0"),
        "lt_high_amount_5": safe_get_value(results, 10, "total_count", "0"),
        "lt_high_amount_10": safe_get_value(results, 11, "total_count", "0"),
        "lt_high_amount_100": safe_get_value(results, 12, "total_count", "0"),

        # 移动相关数据
        "yd_telephone_amount": safe_get_value(results, 24, "total_money", "0"),
        "yd_high_amount_5": safe_get_value(results, 13, "total_count", "0"),
        "yd_high_amount_10": safe_get_value(results, 14, "total_count", "0"),
        "yd_high_amount_100": safe_get_value(results, 15, "total_count", "0"),

        # 广电相关数据
        "gd_telephone_amount": safe_get_value(results, 25, "total_money", "0"),
        "gd_high_amount_5": safe_get_value(results, 16, "total_count", "0"),
        "gd_high_amount_10": safe_get_value(results, 17, "total_count", "0"),
        "gd_high_amount_100": safe_get_value(results, 18, "total_count", "0"),

        # 虚商相关数据
        "xs_telephone_amount": safe_get_value(results, 26, "total_money", "0"),
        "xs_high_amount_5": safe_get_value(results, 19, "total_count", "0"),
        "xs_high_amount_10": safe_get_value(results, 20, "total_count", "0"),
        "xs_high_amount_100": safe_get_value(results, 21, "total_count", "0"),

        # 手机信息列表
        "dx_phone_list_50": text_results[0] if len(text_results) > 0 else [],
        "dx_phone_list_100": text_results[1] if len(text_results) > 1 else [],
        "dx_phone_list_1000": text_results[2] if len(text_results) > 2 else [],

        "lt_phone_list_50": text_results[3] if len(text_results) > 3 else [],
        "lt_phone_list_100": text_results[4] if len(text_results) > 4 else [],
        "lt_phone_list_1000": text_results[5] if len(text_results) > 5 else [],

        "yd_phone_list_50": text_results[6] if len(text_results) > 6 else [],
        "yd_phone_list_100": text_results[7] if len(text_results) > 7 else [],
        "yd_phone_list_1000": text_results[8] if len(text_results) > 8 else [],

        "gd_phone_list_50": text_results[9] if len(text_results) > 9 else [],
        "gd_phone_list_100": text_results[10] if len(text_results) > 10 else [],
        "gd_phone_list_1000": text_results[11] if len(text_results) > 11 else [],

        "xs_phone_list_50": text_results[12] if len(text_results) > 12 else [],
        "xs_phone_list_100": text_results[13] if len(text_results) > 13 else [],
        "xs_phone_list_1000": text_results[14] if len(text_results) > 14 else [],

        "phone_height_1000": text_results[15] if len(text_results) > 15 else [],

        # 运营商统计数据
        "phone_total": str(len(excel_data)) if excel_data else "0",
        "tele_phone_total": str(data_amount.get('电信', {}).get('总数', 0)),
        "tele_phone_proportion": str(data_amount.get('电信', {}).get('占比', 0)),
        "move_phone_total": str(data_amount.get('移动', {}).get('总数', 0)),
        "move_phone_proportion": str(data_amount.get('移动', {}).get('占比', 0)),
        "unicom_phone_total": str(data_amount.get('联通', {}).get('总数', 0)),
        "unicom_phone_proportion": str(data_amount.get('联通', {}).get('占比', 0)),
        "sva_phone_total": str(data_amount.get('广电', {}).get('总数', 0)),
        "sva_phone_proportion": str(data_amount.get('广电', {}).get('占比', 0)),
        "vc_phone_total": str(data_amount.get('虚商', {}).get('总数', 0)),
        "vc_phone_proportion": str(data_amount.get('虚商', {}).get('占比', 0)),
    }


def safe_get_value(results, index, key, default="0"):
    """
    安全获取查询结果中的值

    :param results: 查询结果列表
    :param index: 结果索引
    :param key: 字段名
    :param default: 默认值
    :return: 字段值或默认值
    """
    try:
        if (isinstance(results, list) and
                len(results) > index and
                isinstance(results[index], list) and
                len(results[index]) > 0 and
                isinstance(results[index][0], dict)):
            value = results[index][0].get(key)
            return str(value) if value is not None else default
        return default
    except (IndexError, KeyError, AttributeError) as e:
        logger.warning(f"获取值失败 - index: {index}, key: {key}, error: {e}")
        return default


def safe_get_result(data, index, key=None, default=""):
    """
    安全获取 text_results 中的数据

    :param data: 查询结果列表，如 text_results[index]
    :param index: 外层索引
    :param key: 字段名，例如 'suspect_phone_info'
    :param default: 默认值
    :return: 提取的字符串或默认值
    """
    try:
        if isinstance(data, list) and len(data) > index:
            item = data[index]
            if isinstance(item, list) and len(item) > 0:
                value = item[0].get(key) if key else item[0]
                return str(value) if value is not None else default
        return default
    except Exception as e:
        logger.warning(f"获取结果失败 - index: {index}, key: {key}, error: {e}")
        return default


def execute_queries(queries):
    """
    执行多个SQL查询并返回结果数组
    :param queries: SQL查询语句列表
    :return: 查询结果的字典列表
    """
    results = []
    for i, query in enumerate(queries):
        try:
            with engine.connect() as connection:
                result = connection.execute(query)
                rows = result.fetchall()
                columns = result.keys()
                result_dict = [dict(zip(columns, row)) for row in rows]
                results.append(result_dict)
        except Exception as e:
            logger.error(f"执行查询 {i} 失败: {e}")
            logger.error(f"查询语句: {str(query)}")
            # 添加空结果以保持索引一致性
            results.append([])

    return results


if __name__ == '__main__':
    try:
        get_data()
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise