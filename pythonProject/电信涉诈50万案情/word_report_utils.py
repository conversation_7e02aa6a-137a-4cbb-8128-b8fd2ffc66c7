from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_PARAGRAPH_ALIGNMENT
import io
from flask import send_file
from docx.shared import Pt, Cm
from docx.oxml.ns import qn
from docx.shared import RGBColor
import ast
import json
import matplotlib.pyplot as plt
import io
from collections import defaultdict
import logging


class WordReportUtils:
    @staticmethod
    def generate_case_analysis_report(data, file_stream):
        """
        生成案情分析报告 Word 文件
        :param data: 包含案件数据的字典，必须包含以下键：
                     date_range, overall_situation, case_distribution_desc,
                     case_types, app_case_desc, app_top_list, loss_distribution
        :param file_stream: 文件流对象，用于保存生成的Word文档
        :return: None (直接保存到file_stream)
        """
        try:
            doc = Document()

            # 设置文档默认样式
            WordReportUtils._setup_document_styles(doc)

            # 添加报告标题
            WordReportUtils._add_report_title(doc)

            # 添加案情总体形势
            WordReportUtils._add_overall_situation(doc, data)

            # 添加分布情况
            WordReportUtils._add_distribution_analysis(doc, data)

            # 添加重点监测
            WordReportUtils._add_key_monitoring(doc, data)

            # 添加APP涉案分析
            WordReportUtils._add_app_analysis(doc, data)

            # 添加重大案件分析
            WordReportUtils._add_major_case_analysis(doc, data)

            # 添加案件分布类型
            WordReportUtils._add_case_distribution_types(doc, data)

            # 添加附件表
            WordReportUtils._add_appendix_tables(doc, data)

            # 保存到文件流
            doc.save(file_stream)

        except Exception as e:
            logging.error(f"生成案情分析报告失败: {str(e)}")
            raise RuntimeError("生成案情分析报告失败") from e

    @staticmethod
    def _setup_document_styles(doc):
        """设置文档默认样式"""
        # 设置默认样式（正文）
        style = doc.styles['Normal']
        font = style.font
        font.name = '仿宋_GB2312'
        font.size = Pt(12)  # 3号字体大小（pt）
        font.color.rgb = RGBColor(0, 0, 0)
        style._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')
        style.paragraph_format.first_line_indent = Cm(0.67)

        # 设置标题样式：Heading 1 和 Heading 2
        for heading_style in ['Heading 1', 'Heading 2']:
            h_style = doc.styles[heading_style]
            h_font = h_style.font
            h_font.name = '黑体'
            h_font.size = Pt(15)
            h_font.color.rgb = RGBColor(0, 0, 0)
            h_style._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')

    @staticmethod
    def _add_report_title(doc):
        """添加报告标题"""
        title = doc.add_heading('案情分析日报', level=1)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        for run in title.runs:
            run.font.name = 'SimSun'
            run.font.size = Pt(20)
            run.font.bold = True
            run.font.color.rgb = RGBColor(0, 0, 0)
            run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')

    @staticmethod
    def _add_overall_situation(doc, data):
        """添加案情总体形势章节"""
        title_two = doc.add_heading('一、案情总体形势', level=2)
        WordReportUtils._format_heading(title_two)

        # 构建高金额案件描述
        height_data = data.get("phone_height_1000", [])
        length = len(height_data)
        high_amount_desc = f"涉案金额大于1000万以上案件{length}起"
        if length > 0:
            case_numbers = "，".join([item["case_number"] for item in height_data])
            high_amount_desc += f"，案件编号：{case_numbers}。"
        else:
            high_amount_desc += "。"

        # 添加总体情况段落
        overall_text = (
            f"{data.get('date_range', '')}共接收案情{data.get('case_count', '0')}条，"
            f"案情涉案总金额{data.get('loss_amount', '')}亿元。本日案情{data.get('case_count', '0')}条，"
            f"环比昨日{data.get('day_hb', '')}%。本月累计案情{data.get('mon_count', '0')}条，环比上月{data.get('mon_hb', '')}%。"
            f"中国电信手机号{data.get('dx_sa_count', '')}个，环比昨日{data.get('dx_hb_count', '')}%。"
            f"{high_amount_desc}"
        )
        WordReportUtils._add_paragraph(doc, overall_text)

        # 添加涉诈APP情况
        app_text = (
            f"【涉诈APP情况】今日录入涉诈APP总数{data.get('app_now_count', '')}，涉案{data.get('app_total', '')}起，"
            f"金额{data.get('app_now_money', '')}万元，{data.get('add_app_str', '')}。"
        )
        WordReportUtils._add_paragraph(doc, app_text)

    @staticmethod
    def _add_distribution_analysis(doc, data):
        """添加分布情况章节"""
        title_th = doc.add_heading('二、分布情况', level=2)
        WordReportUtils._format_heading(title_th)

        # 1. 涉案类型
        case_type_text = (
            f"1、涉案类型。其中url类涉案{data.get('url_count', '')}起"
            f"；APP类涉案{data.get('app_count', '')}起；"
            f"电话、短信引流{data.get('sms_count', '')}起；"
            f"国际号码涉案{data.get('intel_count', '')}起，106开头行短端口涉案{data.get('international_count', '')}起。"
        )
        WordReportUtils._add_paragraph(doc, case_type_text)

        # 2. 运营商涉案手机号码
        operator_text = (
            f"2.运营商涉案手机号码：本日运营商涉案手机记录{data.get('phone_total', '')}个，"
            f"电信{data.get('tele_phone_total', '')}个（占比{data.get('tele_phone_proportion', '')}%），"
            f"本月累计{data.get('dx_month_count', '')}起（占比{data.get('dx_month_round', '')}%）；"
            f"联通{data.get('unicom_phone_total', '')}个（占比{data.get('unicom_phone_proportion', '')}%）；"
            f"移动{data.get('move_phone_total', '')}个（占比{data.get('move_phone_proportion', '')}%）；"
            f"广电{data.get('sva_phone_total', '')}个（占比{data.get('sva_phone_proportion', '')}%）；"
            f"虚商{data.get('vc_phone_total', '')}个（占比{data.get('vc_phone_proportion', '')}%）；"
        )
        WordReportUtils._add_paragraph(doc, operator_text)

        # 添加趋势图
        WordReportUtils._add_trend_chart(doc, data)

        # 3. 短信端口涉案
        sms_text = (
            f"3.短信端口涉案：行业短信端口涉案共{data.get('international_count', '')}起,"
            f"本月累计{data.get('international_count_mon', '')}起。"
        )
        WordReportUtils._add_paragraph(doc, sms_text)

        # 4. 50万以上案情
        high_amount_text = (
            f"4.50万以上案情{data.get('case_50_up', '')}起,运营商相关{data.get('operator_50_up', '')}起，"
            f"其中电信{data.get('operator_dx_50_up', '')}起。"
        )
        WordReportUtils._add_paragraph(doc, high_amount_text)

    @staticmethod
    def _add_trend_chart(doc, data):
        """添加涉案数量趋势图"""
        try:
            # 设置中文字体支持
            plt.rcParams['font.sans-serif'] = ['SimHei']
            plt.rcParams['axes.unicode_minus'] = False

            # 创建折线图
            plt.figure(figsize=(12, 6))  # 设置图形大小

            # 绘制各运营商趋势线
            operators = [
                ('dx', '电信涉案数量'),
                ('yd', '移动涉案数量'),
                ('lt', '联通涉案数量'),
                ('gd', '广电涉案数量'),
                ('xs', '虚商涉案数量')
            ]

            for op_code, op_name in operators:
                x_data = data.get(f"{op_code}_x_data", [])
                y_data = data.get(f"{op_code}_y_data", [])
                if x_data and y_data:  # 只有在有数据时才绘制
                    plt.plot(x_data, y_data, label=op_name, marker='o')

            plt.title('涉案数量趋势图')
            plt.xlabel('日期')
            plt.ylabel('涉案数量')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.legend()
            plt.grid(True, alpha=0.3)  # 添加网格，透明度设置

            # 将图像保存到 BytesIO 对象
            image_stream = io.BytesIO()
            plt.savefig(image_stream, format='png', dpi=300, bbox_inches='tight')  # 提高分辨率
            plt.close()  # 关闭图形以释放内存
            image_stream.seek(0)

            # 在文档中插入图像
            doc.add_picture(image_stream, width=Cm(12))

        except Exception as e:
            logging.error(f"生成趋势图失败: {str(e)}")
            # 如果图表生成失败，添加错误提示
            WordReportUtils._add_paragraph(doc, "【趋势图生成失败】")

    @staticmethod
    def _add_key_monitoring(doc, data):
        """添加重点监测章节"""
        title_th = doc.add_heading('三、重点监测', level=2)
        WordReportUtils._format_heading(title_th)

        monitoring_text = f"1.{data.get('key_monitor_data', '')}涉诈案件{data.get('key_monitor_count', '')}起。"
        WordReportUtils._add_paragraph(doc, monitoring_text)

    @staticmethod
    def _add_app_analysis(doc, data):
        """添加APP涉案分析章节"""
        title_th = doc.add_heading('四、APP涉案分析', level=2)
        WordReportUtils._format_heading(title_th)

        trend_text = f"1.趋势分析：本日案件APP涉案{data.get('app_total', '')}起，{data.get('app_list_top10_str', '')}"
        WordReportUtils._add_paragraph(doc, trend_text)

        detail_text = f"2.{data.get('app_str_list', '')}"
        WordReportUtils._add_paragraph(doc, detail_text)

        WordReportUtils._add_app_top_ten_table(doc, data.get("app_list_top10", []))

    @staticmethod
    def _add_major_case_analysis(doc, data):
        """添加重大案件分析章节"""
        title_th = doc.add_heading('五、重大案件分析', level=2)
        WordReportUtils._format_heading(title_th)

        # 各运营商重大案件分析
        operators = [
            ('', '电信'),
            ('_lt', '联通'),
            ('_yd', '移动'),
            ('_gd', '广电'),
            ('_xs', '虚商')
        ]

        for suffix, name in operators:
            description = WordReportUtils._build_operator_case_description(data, suffix, name)
            WordReportUtils._add_paragraph(doc, description)

        # 添加手机号码省份统计表
        WordReportUtils._phone_province_table(doc, data)

    @staticmethod
    def _add_case_distribution_types(doc, data):
        """添加案件分布类型章节"""
        title_th = doc.add_heading('六、案件分布类型', level=2)
        WordReportUtils._format_heading(title_th)

        distribution_text = f"本日案件高发的5种案件类型分别为；{data.get('case_distribution_desc', '')}"
        WordReportUtils._add_paragraph(doc, distribution_text)

    @staticmethod
    def _add_appendix_tables(doc, data):
        """添加附件表章节"""
        title_th = doc.add_heading('七、附件表', level=2)
        WordReportUtils._format_heading(title_th)

        # 添加案件类型表格
        WordReportUtils._add_paragraph(doc, "1．案件类型排序见下表：")
        WordReportUtils._add_case_table(doc, data.get("case_types", []))

        # 添加APP涉案表格
        WordReportUtils._add_paragraph(doc, "2.涉诈APP TOP20详情如下：")
        WordReportUtils._add_app_table(doc, data.get("app_top_list", []))

    @staticmethod
    def _format_heading(heading):
        """格式化标题样式"""
        for run in heading.runs:
            run.font.name = 'SimSun'
            run.font.size = Pt(15)
            run.font.bold = True
            run.font.color.rgb = RGBColor(0, 0, 0)
            run._element.rPr.rFonts.set(qn('w:eastAsia'), 'SimSun')

    @staticmethod
    def _build_operator_case_description(data, suffix, operator_name):
        """构建运营商案件描述"""
        desc_parts = []

        # 基础信息
        amount_key = f"{'telephone_amount' if not suffix else operator_name.lower() + '_telephone_amount'}"
        desc_parts.append(f"其中中国{operator_name}号码涉案金额{data.get(amount_key, '')}万")

        # 不同金额档案件统计
        amount_levels = [
            ('5', '50万以上', f"{operator_name.lower() if suffix else 'dx'}_phone_list_50"),
            ('10', '100万以上', f"{operator_name.lower() if suffix else 'dx'}_phone_list_100"),
            ('100', '1000万以上', f"{operator_name.lower() if suffix else 'dx'}_phone_list_1000")
        ]

        for level, desc, phone_list_key in amount_levels:
            count_key = f"{'high_amount_' + level if not suffix else suffix[1:] + '_high_amount_' + level}"
            count = data.get(count_key, "0")
            desc_parts.append(f"，{desc}的案件{count}个")

            if count != "0":
                phones = WordReportUtils.format_telecom_phones(
                    data.get(phone_list_key, []),
                    operator_name
                )
                if phones:
                    desc_parts.append(f"：{phones}")
                else:
                    desc_parts.append("。")
            else:
                desc_parts.append("。" if level == "100" else "")

        return "".join(desc_parts)

    @staticmethod
    def _add_paragraph(doc, text):
        """添加普通段落"""
        if text:
            paragraph = doc.add_paragraph(text)
            paragraph.alignment = WD_PARAGRAPH_ALIGNMENT.JUSTIFY

    @staticmethod
    def _add_centered_paragraph(doc, text):
        """添加居中段落"""
        if text:
            p = doc.add_paragraph(text)
            p.alignment = WD_ALIGN_PARAGRAPH.CENTER

    @staticmethod
    def _add_case_table(doc, case_data):
        """添加案件类型表格"""
        if not case_data:
            return

        table = doc.add_table(rows=1, cols=5)
        table.style = 'Table Grid'
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '序号'
        hdr_cells[1].text = '类型'
        hdr_cells[2].text = '总数（起）'
        hdr_cells[3].text = '金额(万元)'
        hdr_cells[4].text = '占比（%）'

        for item in case_data:
            row_cells = table.add_row().cells
            row_cells[0].text = str(int(float(item["id"]))) if isinstance(item["id"], (int, float, str)) and str(
                item["id"]).replace('.', '').isdigit() else str(item["id"])
            row_cells[1].text = str(item.get("type", ""))
            row_cells[2].text = str(item.get("total_count", ""))
            row_cells[3].text = str(item.get("amount", ""))
            row_cells[4].text = str(item.get("percentage", ""))

        WordReportUtils.set_table_font_style(table)

    @staticmethod
    def _phone_province_table(doc, data):
        """添加手机号码省份统计表"""
        table = doc.add_table(rows=1, cols=4)
        table.style = 'Table Grid'
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '金额区间'
        hdr_cells[1].text = '地域'
        hdr_cells[2].text = '数量'
        hdr_cells[3].text = '具体号码及归属地'

        # 收集所有运营商的统计数据
        phone_lists = []
        operators = [
            ('dx', '中国电信', '电信'),
            ('lt', '中国联通', '联通'),
            ('yd', '中国移动', '移动'),
            ('gd', '中国广电', '广电'),
            ('xs', '中国虚商', '虚商')
        ]

        for op_code, op_full_name, op_short_name in operators:
            for amount in ['50', '100', '1000']:
                phone_list_key = f"{op_code}_phone_list_{amount}"
                amount_desc = f"{op_full_name}-{amount}万以上"
                phone_data = WordReportUtils._phone_province_group(
                    data.get(phone_list_key, []),
                    amount_desc,
                    op_short_name
                )
                phone_lists.extend(phone_data)

        # 添加表格行
        for item in phone_lists:
            row_cells = table.add_row().cells
            row_cells[0].text = str(item.get('金额区间', ''))
            row_cells[1].text = str(item.get('地域', ''))
            row_cells[2].text = str(item.get('数量', ''))
            row_cells[3].text = str(item.get('具体号码及归属地', ''))

        WordReportUtils.set_table_font_style(table)

    @staticmethod
    def _phone_province_group(data, my_str, operator_type):
        """按省份分组手机号码统计"""
        result = []
        grouped = defaultdict(list)

        if not data:
            result.append({
                '金额区间': my_str,
                '地域': '无',
                '数量': 0,
                '具体号码及归属地': '无'
            })
            return result

        try:
            for da in data:
                suspect_phone_info = da.get('suspect_phone_info', '[]')
                if isinstance(suspect_phone_info, str):
                    phone_info_list = json.loads(suspect_phone_info)
                else:
                    phone_info_list = suspect_phone_info

                for item in phone_info_list:
                    if item.get('operator') == operator_type:
                        province = item.get('province', '未知')
                        phone = item.get('phone', '')
                        key = (my_str, str(province))
                        phone_with_province = f"{phone}({province})"
                        grouped[key].append(phone_with_province)

        except (json.JSONDecodeError, KeyError, TypeError) as e:
            logging.error(f"解析手机号码信息失败: {str(e)}")
            result.append({
                '金额区间': my_str,
                '地域': '解析错误',
                '数量': 0,
                '具体号码及归属地': '数据解析失败'
            })
            return result

        # 构建结果
        for key, phone_list in grouped.items():
            result.append({
                '金额区间': key[0],
                '地域': key[1],
                '数量': len(phone_list),
                '具体号码及归属地': "、".join(phone_list)
            })

        return result

    @staticmethod
    def _add_app_top_ten_table(doc, case_data):
        """添加APP TOP10表格"""
        if not case_data:
            return

        table = doc.add_table(rows=1, cols=4)
        table.style = 'Table Grid'
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '序号'
        hdr_cells[1].text = 'APP名称'
        hdr_cells[2].text = '总数（起）'
        hdr_cells[3].text = '涉案金额'

        for num, item in enumerate(case_data, 1):
            row_cells = table.add_row().cells
            row_cells[0].text = str(num)
            row_cells[1].text = str(item.get("app_name", ""))
            row_cells[2].text = str(item.get("num", ""))
            row_cells[3].text = str(item.get("amount", ""))

        WordReportUtils.set_table_font_style(table)

    @staticmethod
    def _add_app_table(doc, app_data):
        """添加APP详情表格"""
        if not app_data:
            return

        table = doc.add_table(rows=1, cols=6)
        table.style = 'Table Grid'
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '序号'
        hdr_cells[1].text = 'APP名称'
        hdr_cells[2].text = '总数（起）'
        hdr_cells[3].text = '占比（%）'
        hdr_cells[4].text = '30天累计（起）'
        hdr_cells[5].text = '是否新增'

        for item in app_data:
            row_cells = table.add_row().cells
            row_cells[0].text = str(int(float(item["id"]))) if isinstance(item["id"], (int, float, str)) and str(
                item["id"]).replace('.', '').isdigit() else str(item["id"])
            row_cells[1].text = str(item.get("app_name", ""))
            row_cells[2].text = str(item.get("total_count", ""))
            row_cells[3].text = str(item.get("percentage", ""))
            row_cells[4].text = str(item.get("accumulate", ""))
            row_cells[5].text = str(item.get("is_addition", ""))

        WordReportUtils.set_table_font_style(table)

    @staticmethod
    def set_table_font_style(table, font_name='仿宋_GB2312', title_font_size=Pt(12), font_size=Pt(10.5),
                             font_color=RGBColor(0, 0, 0)):
        """设置表格字体样式"""
        header_widths = {
            '序号': 1.6,
            '数量': 1.6,
            '地域': 1.6,
            '总数（起）': 2.6,
            '占比（%）': 2.6,
        }

        table.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 设置表头样式
        for cell in table.rows[0].cells:
            cell.paragraphs[0].paragraph_format.first_line_indent = Pt(0)
            cell.paragraphs[0].paragraph_format.line_spacing = Pt(12)
            cell.paragraphs[0].paragraph_format.space_after = Pt(0)
            cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER

            for run in cell.paragraphs[0].runs:
                run.font.name = font_name
                run.font.size = title_font_size
                run.font.bold = True
                run.font.color.rgb = font_color
                run._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)

        # 设置表格内容样式
        for row in table.rows[1:]:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    paragraph.paragraph_format.first_line_indent = Pt(0)
                    paragraph.paragraph_format.line_spacing = Pt(12)
                    paragraph.paragraph_format.space_after = Pt(0)
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                    for run in paragraph.runs:
                        run.font.name = font_name
                        run.font.size = font_size
                        run.font.color.rgb = font_color
                        run._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)

        # 设置列宽
        for col_index, column_cells in enumerate(table.columns):
            if col_index < len(table.rows[0].cells):
                header_text = table.rows[0].cells[col_index].text
                if header_text in header_widths:
                    for cell in table.columns[col_index].cells:
                        cell.width = Cm(header_widths[header_text])

    @staticmethod
    def send_word_report(file_stream, filename="CaseAnalysisReport.docx"):
        """发送Word报告文件"""
        return send_file(
            file_stream,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )

    @staticmethod
    def format_telecom_phones(results, operator_type):
        """
        从查询结果中筛选出指定运营商的手机号，并按归属地拼接成指定格式的字符串。

        :param results: 查询结果列表
        :param operator_type: 运营商类型
        :return: 拼接后的字符串
        """
        if not results:
            return ""

        telecom_phones = []

        try:
            for item in results:
                suspect_phone_info = item.get("suspect_phone_info", "[]")
                if isinstance(suspect_phone_info, str):
                    phone_info_list = json.loads(suspect_phone_info)
                else:
                    phone_info_list = suspect_phone_info

                for info in phone_info_list:
                    if info.get("operator") == operator_type:
                        phone = info.get('phone', '')
                        province = info.get('province', '未知')
                        telecom_phones.append(f"{phone}(归属地{province})")

        except (json.JSONDecodeError, KeyError, TypeError) as e:
            logging.error(f"解析手机号码信息失败: {str(e)}")
            return "数据解析失败"

        return "、".join(telecom_phones)

        @staticmethod
        def format_amount(amount):
            """
            格式化金额显示

            :param amount: 金额数值
            :return: 格式化后的金额字符串
            """
            try:
                if not amount or amount == '' or amount == '0':
                    return '0'

                # 处理字符串类型的金额
                if isinstance(amount, str):
                    amount = float(amount.replace(',', ''))

                # 格式化为千分位显示
                if amount >= 10000:
                    return f"{amount / 10000:.2f}万"
                else:
                    return f"{amount:,.2f}"

            except (ValueError, TypeError):
                return str(amount)

        @staticmethod
        def validate_data_structure(data):
            """
            验证输入数据结构的完整性

            :param data: 输入的数据字典
            :return: 验证结果和错误信息
            """
            required_fields = [
                'date_range', 'case_count', 'loss_amount', 'day_hb', 'mon_count',
                'mon_hb', 'dx_sa_count', 'dx_hb_count', 'app_now_count', 'app_total',
                'app_now_money', 'url_count', 'app_count', 'sms_count', 'intel_count',
                'international_count', 'phone_total', 'case_types', 'app_top_list'
            ]

            missing_fields = []
            for field in required_fields:
                if field not in data:
                    missing_fields.append(field)

            if missing_fields:
                return False, f"缺少必要字段: {', '.join(missing_fields)}"

            return True, "数据结构验证通过"

        @staticmethod
        def add_page_break(doc):
            """
            添加分页符

            :param doc: Word文档对象
            """
            doc.add_page_break()

        @staticmethod
        def add_footer(doc, footer_text="机密文件 - 内部使用"):
            """
            添加页脚

            :param doc: Word文档对象
            :param footer_text: 页脚文本
            """
            try:
                section = doc.sections[0]
                footer = section.footer
                footer_paragraph = footer.paragraphs[0]
                footer_paragraph.text = footer_text
                footer_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                # 设置页脚字体样式
                for run in footer_paragraph.runs:
                    run.font.name = '仿宋_GB2312'
                    run.font.size = Pt(9)
                    run.font.color.rgb = RGBColor(128, 128, 128)
                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')

            except Exception as e:
                logging.warning(f"添加页脚失败: {str(e)}")

        @staticmethod
        def add_header(doc, header_text="反诈骗中心案情分析系统"):
            """
            添加页眉

            :param doc: Word文档对象
            :param header_text: 页眉文本
            """
            try:
                section = doc.sections[0]
                header = section.header
                header_paragraph = header.paragraphs[0]
                header_paragraph.text = header_text
                header_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                # 设置页眉字体样式
                for run in header_paragraph.runs:
                    run.font.name = '仿宋_GB2312'
                    run.font.size = Pt(10)
                    run.font.color.rgb = RGBColor(100, 100, 100)
                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')

            except Exception as e:
                logging.warning(f"添加页眉失败: {str(e)}")

        @staticmethod
        def set_document_margins(doc, top=2.54, bottom=2.54, left=3.17, right=3.17):
            """
            设置文档页边距

            :param doc: Word文档对象
            :param top: 上边距(cm)
            :param bottom: 下边距(cm)
            :param left: 左边距(cm)
            :param right: 右边距(cm)
            """
            try:
                section = doc.sections[0]
                section.top_margin = Cm(top)
                section.bottom_margin = Cm(bottom)
                section.left_margin = Cm(left)
                section.right_margin = Cm(right)
            except Exception as e:
                logging.warning(f"设置页边距失败: {str(e)}")

        @staticmethod
        def calculate_percentage(value, total):
            """
            计算百分比

            :param value: 当前值
            :param total: 总值
            :return: 百分比字符串
            """
            try:
                if not total or total == 0:
                    return "0.00"

                percentage = (float(value) / float(total)) * 100
                return f"{percentage:.2f}"

            except (ValueError, TypeError, ZeroDivisionError):
                return "0.00"

        @staticmethod
        def generate_summary_statistics(data):
            """
            生成汇总统计信息

            :param data: 输入数据
            :return: 统计信息字典
            """
            try:
                stats = {
                    'total_cases': data.get('case_count', 0),
                    'total_amount': data.get('loss_amount', 0),
                    'high_amount_cases': len(data.get('phone_height_1000', [])),
                    'app_cases': data.get('app_count', 0),
                    'url_cases': data.get('url_count', 0),
                    'sms_cases': data.get('sms_count', 0)
                }

                # 计算各类案件占比
                total = int(stats['total_cases']) if stats['total_cases'] else 1
                stats['app_percentage'] = WordReportUtils.calculate_percentage(stats['app_cases'], total)
                stats['url_percentage'] = WordReportUtils.calculate_percentage(stats['url_cases'], total)
                stats['sms_percentage'] = WordReportUtils.calculate_percentage(stats['sms_cases'], total)

                return stats

            except Exception as e:
                logging.error(f"生成统计信息失败: {str(e)}")
                return {}

        @staticmethod
        def export_to_pdf(doc_stream, pdf_path):
            """
            将Word文档转换为PDF (需要额外的库支持)
            注意: 此方法需要安装python-docx2pdf或使用其他PDF转换工具

            :param doc_stream: Word文档流
            :param pdf_path: PDF输出路径
            """
            try:
                # 这里需要根据实际使用的PDF转换库来实现
                # 示例使用docx2pdf (需要Microsoft Word或LibreOffice)
                # from docx2pdf import convert
                # convert(doc_stream, pdf_path)

                logging.info("PDF转换功能需要额外配置相关库")
                pass

            except Exception as e:
                logging.error(f"PDF转换失败: {str(e)}")
                raise

        @staticmethod
        def add_watermark(doc, watermark_text="内部文件"):
            """
            添加水印 (简化版本，完整实现需要更复杂的XML操作)

            :param doc: Word文档对象
            :param watermark_text: 水印文本
            """
            try:
                # 简化的水印实现，实际可能需要更复杂的XML操作
                logging.info(f"水印功能: {watermark_text} (需要完整实现)")
                # 这里可以添加具体的水印实现代码
                pass

            except Exception as e:
                logging.warning(f"添加水印失败: {str(e)}")

        @staticmethod
        def cleanup_temp_files():
            """
            清理临时文件
            """
            try:
                # 清理matplotlib临时文件
                plt.close('all')
                logging.info("临时文件清理完成")

            except Exception as e:
                logging.warning(f"清理临时文件失败: {str(e)}")

        @staticmethod
        def get_report_metadata(data):
            """
            获取报告元数据信息

            :param data: 输入数据
            :return: 元数据字典
            """
            try:
                from datetime import datetime

                metadata = {
                    'generation_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'report_date': data.get('date_range', ''),
                    'total_cases': data.get('case_count', 0),
                    'report_version': '1.0',
                    'system': '反诈骗案情分析系统'
                }

                return metadata

            except Exception as e:
                logging.error(f"获取报告元数据失败: {str(e)}")
                return {}

    # 使用示例和工具函数
    class ReportGenerator:
        """报告生成器主类"""

        def __init__(self):
            self.utils = WordReportUtils()

        def generate_report(self, data, output_path=None):
            """
            生成完整报告

            :param data: 报告数据
            :param output_path: 输出路径，如果为None则返回文件流
            :return: 文件流或保存路径
            """
            try:
                # 验证数据结构
                is_valid, message = WordReportUtils.validate_data_structure(data)
                if not is_valid:
                    raise ValueError(f"数据验证失败: {message}")

                # 创建文件流
                file_stream = io.BytesIO()

                # 生成报告
                WordReportUtils.generate_case_analysis_report(data, file_stream)

                # 清理临时文件
                WordReportUtils.cleanup_temp_files()

                if output_path:
                    # 保存到指定路径
                    file_stream.seek(0)
                    with open(output_path, 'wb') as f:
                        f.write(file_stream.read())
                    return output_path
                else:
                    # 返回文件流
                    file_stream.seek(0)
                    return file_stream

            except Exception as e:
                logging.error(f"报告生成失败: {str(e)}")
                raise

        def get_sample_data(self):
            """
            获取示例数据结构

            :return: 示例数据字典
            """
            return {
                'date_range': '2024年7月2日',
                'case_count': '156',
                'loss_amount': '2.34',
                'day_hb': '+12.5',
                'mon_count': '4523',
                'mon_hb': '****',
                'dx_sa_count': '89',
                'dx_hb_count': '+15.2',
                'phone_height_1000': [],
                'app_now_count': '23',
                'app_total': '67',
                'app_now_money': '234.5',
                'add_app_str': '新增涉诈APP 5个',
                'url_count': '45',
                'app_count': '67',
                'sms_count': '34',
                'intel_count': '12',
                'international_count': '8',
                'phone_total': '234',
                'tele_phone_total': '89',
                'tele_phone_proportion': '38.0',
                'dx_month_count': '1234',
                'dx_month_round': '42.5',
                'unicom_phone_total': '67',
                'unicom_phone_proportion': '28.6',
                'move_phone_total': '56',
                'move_phone_proportion': '23.9',
                'sva_phone_total': '12',
                'sva_phone_proportion': '5.1',
                'vc_phone_total': '10',
                'vc_phone_proportion': '4.3',
                'case_types': [
                    {'id': 1, 'type': '网络贷款', 'total_count': '45', 'amount': '567.8', 'percentage': '28.8'},
                    {'id': 2, 'type': '刷单返利', 'total_count': '38', 'amount': '423.2', 'percentage': '24.4'}
                ],
                'app_top_list': [
                    {'id': 1, 'app_name': '某贷款APP', 'total_count': '12', 'percentage': '7.7', 'accumulate': '45',
                     'is_addition': '是'},
                    {'id': 2, 'app_name': '某投资APP', 'total_count': '8', 'percentage': '5.1', 'accumulate': '23',
                     'is_addition': '否'}
                ]
            }