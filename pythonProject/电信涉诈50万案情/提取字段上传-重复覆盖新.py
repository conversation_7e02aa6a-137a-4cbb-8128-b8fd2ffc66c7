import pandas as pd
import pymysql
from datetime import datetime
import logging
from typing import Optional, List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def deduplicate_phone_numbers(phone_str: Optional[str]) -> str:
    """
    对联系电话字符串进行去重处理
    例如："18006590695,18006590695,18006590695" -> "18006590695"
         "13632761082,13692251170" -> "13632761082,13692251170"
         "15882880807,15882880807,13880657217" -> "15882880807,13880657217"

    Args:
        phone_str: 电话号码字符串，用逗号分隔

    Returns:
        去重后的电话号码字符串
    """
    if not phone_str or pd.isna(phone_str) or phone_str == '':
        return phone_str or ''

    # 分割电话号码并去重，同时去除空白字符
    phones = [phone.strip() for phone in phone_str.split(',') if phone.strip()]

    # 使用字典保持顺序去重（Python 3.7+）
    unique_phones = list(dict.fromkeys(phones))

    return ','.join(unique_phones)


def match_ages_to_phones(age_str: Optional[str], phone_str: Optional[str]) -> str:
    """
    确保年龄数量与去重后的电话号码数量一致
    处理逻辑：
    1. 如果年龄数量大于电话号码数量，截断年龄列表
    2. 如果年龄数量小于电话号码数量，复制最后一个年龄补足
    3. 如果没有年龄，则用空字符串填充

    Args:
        age_str: 年龄字符串，用逗号分隔
        phone_str: 电话号码字符串，用逗号分隔

    Returns:
        匹配后的年龄字符串
    """
    # 处理空值情况
    ages = [age.strip() for age in age_str.split(',') if age.strip()] if age_str and not pd.isna(age_str) else []
    phones = [phone.strip() for phone in phone_str.split(',') if phone.strip()] if phone_str and not pd.isna(
        phone_str) else []

    phone_count = len(phones)
    age_count = len(ages)

    # 处理各种情况
    if phone_count == 0:
        return ''

    if age_count == 0:
        return ','.join([''] * phone_count)

    if age_count > phone_count:
        # 截断年龄列表
        return ','.join(ages[:phone_count])

    if age_count < phone_count:
        # 复制最后一个年龄补足
        last_age = ages[-1]
        return ','.join(ages + [last_age] * (phone_count - age_count))

    # 数量已经匹配
    return ','.join(ages)


def parse_datetime(value: any, formats: List[str] = None) -> Optional[datetime]:
    """
    解析日期时间值

    Args:
        value: 待解析的日期时间值
        formats: 日期时间格式列表

    Returns:
        解析后的datetime对象或None
    """
    if formats is None:
        formats = ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%Y/%m/%d %H:%M:%S', '%Y/%m/%d']

    if isinstance(value, str) and value.strip() == '':
        return None

    try:
        if isinstance(value, str):
            # 尝试各种日期时间格式
            for fmt in formats:
                try:
                    return datetime.strptime(value.strip(), fmt)
                except ValueError:
                    continue

            # 如果无法解析，尝试 pandas 的 to_datetime
            dt = pd.to_datetime(value, errors='coerce')
            return dt.to_pydatetime() if not pd.isna(dt) else None
        else:
            # 如果已经是 datetime 或 Timestamp 类型
            return pd.Timestamp(value).to_pydatetime()
    except Exception as e:
        logger.warning(f"日期转换错误: {value}, 错误: {e}")
        return None


def main():
    # Excel表头到数据库字段的映射
    header_mapping = {
        '案件编号': 'case_number',
        '案件类别': 'case_category',
        '案件大类': 'case_main_type',
        '案件子类': 'case_sub_type',
        '事件顺序': 'event_sequence',
        '发案时间': 'occurrence_time',
        '发案地区': 'occurrence_area',
        '简要案情': 'brief_case_description',
        '涉案金额': 'involved_amount',
        '录入开始时间': 'entry_start_time',
        '立案时间': 'filing_time',
        '转账方式': 'transfer_method',
        '受害人账号类型': 'victim_account_type',
        '嫌疑人账号类型': 'suspect_account_type',
        '嫌疑人账户名': 'suspect_account_name',
        '嫌疑人账号': 'suspect_account_number',
        '嫌疑人手机号': 'suspect_phone_number',
        '嫌疑人QQ号': 'suspect_qq_number',
        '嫌疑人微信号': 'suspect_wechat_number',
        '嫌疑人其他联系方式': 'suspect_other_contact',
        '嫌疑人服务号类型名称': 'suspect_service_account_type_name',
        '嫌疑人其他联系账号': 'suspect_other_contact_account',
        '服务号网址链接': 'service_account_url',
        '服务号所属公司': 'service_account_company',
        '服务号APP名称': 'service_account_app_name',
        '服务号APP下载链接': 'service_account_app_download_url',
        '网址链接': 'website_url',
        '网址所属公司': 'website_company',
        'APP名称': 'app_name',
        'APP下载链接': 'app_download_url',
        'APP所属公司': 'app_company',
        '联系电话': 'contact_phone',
        '年龄': 'victim_age',
        '网址': 'url',
        '通联关系': 'connection_relationship',
        '诈骗分子APP账号': 'scammer_app_account',
        '受害人APP账号': 'victim_app_account',
        '受害人银行卡号': 'victim_bank_card_number',
        '短信端口号': 'sms_port_number',
        '涉诈手机号运营商信息': 'suspect_phone_info',
        '入库时间': 'insert_day'
    }

    # 数据库连接配置
    db_config = {
        'host': '**************',
        'user': 'FZUser',
        'password': 'fz@********',
        'database': 'antiFraudPlatform',
        'charset': 'utf8mb4'
    }

    # 定义 datetime 类型的字段
    datetime_columns = ['entry_start_time', 'filing_time']

    # Excel文件路径
    excel_file_path = '/Users/<USER>/Documents/日报数据/测试/********全国涉案警情.xlsx'

    try:
        logger.info("开始读取Excel文件...")
        # 读取 Excel 文件
        excel_file = pd.ExcelFile(excel_file_path)
        df = excel_file.parse('sheet1')
        logger.info(f"成功读取Excel文件，共{len(df)}行数据")

        # 获取Excel实际表头并映射到数据库字段
        excel_headers = df.columns.tolist()
        db_columns = [header_mapping.get(h, None) for h in excel_headers]

        # 移除没有映射的字段
        valid_columns = [col for col in db_columns if col is not None]
        valid_indices = [i for i, col in enumerate(db_columns) if col is not None]

        logger.info(f"有效字段数量: {len(valid_columns)}")

        # 处理缺失值
        logger.info("处理缺失值...")
        for col in df.columns:
            if df[col].dtype == 'object':
                df[col].fillna('', inplace=True)
            else:
                df[col].fillna(0, inplace=True)

        # 对联系电话字段进行去重处理
        if '联系电话' in df.columns:
            logger.info("正在对联系电话字段进行去重处理...")
            df['联系电话'] = df['联系电话'].apply(deduplicate_phone_numbers)

            # 确保年龄数量与电话号码数量一致
            if '年龄' in df.columns:
                logger.info("正在匹配年龄与电话号码数量...")
                df['年龄'] = df.apply(lambda x: match_ages_to_phones(x['年龄'], x['联系电话']), axis=1)

        logger.info("连接数据库...")
        # 数据库连接
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()

        # 定义 SQL 插入语句
        column_placeholders = ', '.join(['%s'] * len(valid_columns))
        update_clause = ', '.join([f"{col}=VALUES({col})" for col in valid_columns if col != 'case_number'])
        sql = f"""
        INSERT INTO anti_fraud_case_new ({', '.join(valid_columns)})
        VALUES ({column_placeholders})
        ON DUPLICATE KEY UPDATE {update_clause}
        """

        # 准备插入的数据
        logger.info("准备数据...")
        data_to_insert = []
        for index, row in df.iterrows():
            values = []
            for i in valid_indices:
                col_name = excel_headers[i]
                db_col = header_mapping[col_name]
                value = row[col_name]

                # 处理日期时间类型
                if db_col in datetime_columns:
                    values.append(parse_datetime(value))
                else:
                    values.append(value)
            data_to_insert.append(values)

        # 批量插入数据
        logger.info(f"开始插入{len(data_to_insert)}条数据...")
        cursor.executemany(sql, data_to_insert)

        # 提交事务
        conn.commit()
        logger.info("数据插入成功")

    except FileNotFoundError:
        logger.error(f"Excel文件未找到: {excel_file_path}")
    except pymysql.Error as e:
        logger.error(f"数据插入时出现 MySQL 错误: {e}")
        logger.error(f"错误代码: {e.args[0]}")
        logger.error(f"错误信息: {e.args[1]}")
        if 'conn' in locals():
            conn.rollback()
    except Exception as e:
        logger.error(f"数据插入时出现未知错误: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        # 关闭游标和连接
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
        logger.info("数据库连接已关闭")


if __name__ == "__main__":
    main()