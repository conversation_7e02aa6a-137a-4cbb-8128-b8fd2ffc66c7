import io
import logging
from datetime import datetime
import matplotlib.pyplot as plt

# 导入python-docx相关模块
try:
    from docx import Document
    from docx.shared import Inches, Pt, Cm
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.shared import RGBColor
    from docx.oxml.ns import qn
except ImportError:
    print("请安装python-docx库: pip install python-docx")
    raise


class WordReportUtils:
    """Word报告生成工具类"""

    @staticmethod
    def format_telecom_phones(telecom_phones_list):
        """
        格式化电信运营商电话号码列表

        :param telecom_phones_list: 电信电话号码列表
        :return: 格式化后的字符串
        """
        if not telecom_phones_list:
            return ""
        return "、".join(telecom_phones_list)

    @staticmethod
    def format_amount(amount):
        """
        格式化金额显示

        :param amount: 金额数值
        :return: 格式化后的金额字符串
        """
        try:
            if not amount or amount == '' or amount == '0':
                return '0'

            # 处理字符串类型的金额
            if isinstance(amount, str):
                amount = float(amount.replace(',', ''))

            # 格式化为千分位显示
            if amount >= 10000:
                return f"{amount / 10000:.2f}万"
            else:
                return f"{amount:,.2f}"

        except (ValueError, TypeError):
            return str(amount)

    @staticmethod
    def validate_data_structure(data):
        """
        验证输入数据结构的完整性

        :param data: 输入的数据字典
        :return: 验证结果和错误信息
        """
        required_fields = [
            'date_range', 'case_count', 'loss_amount', 'day_hb', 'mon_count',
            'mon_hb', 'dx_sa_count', 'dx_hb_count', 'app_now_count', 'app_total',
            'app_now_money', 'url_count', 'app_count', 'sms_count', 'intel_count',
            'international_count', 'phone_total', 'case_types', 'app_top_list'
        ]

        missing_fields = []
        for field in required_fields:
            if field not in data:
                missing_fields.append(field)

        if missing_fields:
            return False, f"缺少必要字段: {', '.join(missing_fields)}"

        return True, "数据结构验证通过"

    @staticmethod
    def add_page_break(doc):
        """
        添加分页符

        :param doc: Word文档对象
        """
        doc.add_page_break()

    @staticmethod
    def add_footer(doc, footer_text="机密文件 - 内部使用"):
        """
        添加页脚

        :param doc: Word文档对象
        :param footer_text: 页脚文本
        """
        try:
            section = doc.sections[0]
            footer = section.footer
            footer_paragraph = footer.paragraphs[0]
            footer_paragraph.text = footer_text
            footer_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # 设置页脚字体样式
            for run in footer_paragraph.runs:
                run.font.name = '仿宋_GB2312'
                run.font.size = Pt(9)
                run.font.color.rgb = RGBColor(128, 128, 128)
                run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')

        except Exception as e:
            logging.warning(f"添加页脚失败: {str(e)}")

    @staticmethod
    def add_header(doc, header_text="反诈骗中心案情分析系统"):
        """
        添加页眉

        :param doc: Word文档对象
        :param header_text: 页眉文本
        """
        try:
            section = doc.sections[0]
            header = section.header
            header_paragraph = header.paragraphs[0]
            header_paragraph.text = header_text
            header_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # 设置页眉字体样式
            for run in header_paragraph.runs:
                run.font.name = '仿宋_GB2312'
                run.font.size = Pt(10)
                run.font.color.rgb = RGBColor(100, 100, 100)
                run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')

        except Exception as e:
            logging.warning(f"添加页眉失败: {str(e)}")

    @staticmethod
    def set_document_margins(doc, top=2.54, bottom=2.54, left=3.17, right=3.17):
        """
        设置文档页边距

        :param doc: Word文档对象
        :param top: 上边距(cm)
        :param bottom: 下边距(cm)
        :param left: 左边距(cm)
        :param right: 右边距(cm)
        """
        try:
            section = doc.sections[0]
            section.top_margin = Cm(top)
            section.bottom_margin = Cm(bottom)
            section.left_margin = Cm(left)
            section.right_margin = Cm(right)
        except Exception as e:
            logging.warning(f"设置页边距失败: {str(e)}")

    @staticmethod
    def calculate_percentage(value, total):
        """
        计算百分比

        :param value: 当前值
        :param total: 总值
        :return: 百分比字符串
        """
        try:
            if not total or total == 0:
                return "0.00"

            percentage = (float(value) / float(total)) * 100
            return f"{percentage:.2f}"

        except (ValueError, TypeError, ZeroDivisionError):
            return "0.00"

    @staticmethod
    def generate_summary_statistics(data):
        """
        生成汇总统计信息

        :param data: 输入数据
        :return: 统计信息字典
        """
        try:
            stats = {
                'total_cases': data.get('case_count', 0),
                'total_amount': data.get('loss_amount', 0),
                'high_amount_cases': len(data.get('phone_height_1000', [])),
                'app_cases': data.get('app_count', 0),
                'url_cases': data.get('url_count', 0),
                'sms_cases': data.get('sms_count', 0)
            }

            # 计算各类案件占比
            total = int(stats['total_cases']) if stats['total_cases'] else 1
            stats['app_percentage'] = WordReportUtils.calculate_percentage(stats['app_cases'], total)
            stats['url_percentage'] = WordReportUtils.calculate_percentage(stats['url_cases'], total)
            stats['sms_percentage'] = WordReportUtils.calculate_percentage(stats['sms_cases'], total)

            return stats

        except Exception as e:
            logging.error(f"生成统计信息失败: {str(e)}")
            return {}

    @staticmethod
    def generate_case_analysis_report(data, file_stream):
        """
        生成案情分析报告

        :param data: 报告数据
        :param file_stream: 文件流
        """
        try:
            # 创建Word文档
            doc = Document()

            # 设置文档基本属性
            WordReportUtils.set_document_margins(doc)
            WordReportUtils.add_header(doc)
            WordReportUtils.add_footer(doc)

            # 添加标题
            title = doc.add_heading('反诈骗案情分析报告', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # 添加报告内容
            doc.add_paragraph(f"报告时间范围: {data.get('date_range', '')}")
            doc.add_paragraph(f"案件总数: {data.get('case_count', 0)}")
            doc.add_paragraph(f"损失金额: {WordReportUtils.format_amount(data.get('loss_amount', 0))}")

            # 保存文档到流
            doc.save(file_stream)

        except Exception as e:
            logging.error(f"生成报告失败: {str(e)}")
            raise

    @staticmethod
    def export_to_pdf(doc_stream, pdf_path):
        """
        将Word文档转换为PDF (需要额外的库支持)
        注意: 此方法需要安装python-docx2pdf或使用其他PDF转换工具

        :param doc_stream: Word文档流
        :param pdf_path: PDF输出路径
        """
        try:
            # 这里需要根据实际使用的PDF转换库来实现
            # 示例使用docx2pdf (需要Microsoft Word或LibreOffice)
            # from docx2pdf import convert
            # convert(doc_stream, pdf_path)

            logging.info("PDF转换功能需要额外配置相关库")
            pass

        except Exception as e:
            logging.error(f"PDF转换失败: {str(e)}")
            raise

    @staticmethod
    def add_watermark(doc, watermark_text="内部文件"):
        """
        添加水印 (简化版本，完整实现需要更复杂的XML操作)

        :param doc: Word文档对象
        :param watermark_text: 水印文本
        """
        try:
            # 简化的水印实现，实际可能需要更复杂的XML操作
            logging.info(f"水印功能: {watermark_text} (需要完整实现)")
            # 这里可以添加具体的水印实现代码
            pass

        except Exception as e:
            logging.warning(f"添加水印失败: {str(e)}")

    @staticmethod
    def cleanup_temp_files():
        """
        清理临时文件
        """
        try:
            # 清理matplotlib临时文件
            plt.close('all')
            logging.info("临时文件清理完成")

        except Exception as e:
            logging.warning(f"清理临时文件失败: {str(e)}")

    @staticmethod
    def get_report_metadata(data):
        """
        获取报告元数据信息

        :param data: 输入数据
        :return: 元数据字典
        """
        try:
            metadata = {
                'generation_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'report_date': data.get('date_range', ''),
                'total_cases': data.get('case_count', 0),
                'report_version': '1.0',
                'system': '反诈骗案情分析系统'
            }

            return metadata

        except Exception as e:
            logging.error(f"获取报告元数据失败: {str(e)}")
            return {}


# 使用示例和工具函数
class ReportGenerator:
    """报告生成器主类"""

    def __init__(self):
        self.utils = WordReportUtils()

    def generate_report(self, data, output_path=None):
        """
        生成完整报告

        :param data: 报告数据
        :param output_path: 输出路径，如果为None则返回文件流
        :return: 文件流或保存路径
        """
        try:
            # 验证数据结构
            is_valid, message = WordReportUtils.validate_data_structure(data)
            if not is_valid:
                raise ValueError(f"数据验证失败: {message}")

            # 创建文件流
            file_stream = io.BytesIO()

            # 生成报告
            WordReportUtils.generate_case_analysis_report(data, file_stream)

            # 清理临时文件
            WordReportUtils.cleanup_temp_files()

            if output_path:
                # 保存到指定路径
                file_stream.seek(0)
                with open(output_path, 'wb') as f:
                    f.write(file_stream.read())
                return output_path
            else:
                # 返回文件流
                file_stream.seek(0)
                return file_stream

        except Exception as e:
            logging.error(f"报告生成失败: {str(e)}")
            raise

    def get_sample_data(self):
        """
        获取示例数据结构

        :return: 示例数据字典
        """
        return {
            'date_range': '2024年7月2日',
            'case_count': '156',
            'loss_amount': '2.34',
            'day_hb': '+12.5',
            'mon_count': '4523',
            'mon_hb': '****',
            'dx_sa_count': '89',
            'dx_hb_count': '+15.2',
            'phone_height_1000': [],
            'app_now_count': '23',
            'app_total': '67',
            'app_now_money': '234.5',
            'add_app_str': '新增涉诈APP 5个',
            'url_count': '45',
            'app_count': '67',
            'sms_count': '34',
            'intel_count': '12',
            'international_count': '8',
            'phone_total': '234',
            'tele_phone_total': '89',
            'tele_phone_proportion': '38.0',
            'dx_month_count': '1234',
            'dx_month_round': '42.5',
            'unicom_phone_total': '67',
            'unicom_phone_proportion': '28.6',
            'move_phone_total': '56',
            'move_phone_proportion': '23.9',
            'sva_phone_total': '12',
            'sva_phone_proportion': '5.1',
            'vc_phone_total': '10',
            'vc_phone_proportion': '4.3',
            'case_types': [
                {'id': 1, 'type': '网络贷款', 'total_count': '45', 'amount': '567.8', 'percentage': '28.8'},
                {'id': 2, 'type': '刷单返利', 'total_count': '38', 'amount': '423.2', 'percentage': '24.4'}
            ],
            'app_top_list': [
                {'id': 1, 'app_name': '某贷款APP', 'total_count': '12', 'percentage': '7.7', 'accumulate': '45',
                 'is_addition': '是'},
                {'id': 2, 'app_name': '某投资APP', 'total_count': '8', 'percentage': '5.1', 'accumulate': '23',
                 'is_addition': '否'}
            ]
        }


# 使用示例
if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # 创建报告生成器
    generator = ReportGenerator()

    # 获取示例数据
    sample_data = generator.get_sample_data()

    # 生成报告
    try:
        output_file = "反诈骗案情分析报告.docx"
        result = generator.generate_report(sample_data, output_file)
        print(f"报告生成成功: {result}")
    except Exception as e:
        print(f"报告生成失败: {e}")