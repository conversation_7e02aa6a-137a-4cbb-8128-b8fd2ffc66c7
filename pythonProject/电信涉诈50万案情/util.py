import logging
from typing import List, Dict, Any, Optional
from sqlalchemy import create_engine, text
from sqlalchemy.engine import URL, Engine
from sqlalchemy.exc import SQLAlchemyError

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'drivername': 'mysql+pymysql',
    'username': 'FZUser',
    'password': 'fz@20250324',
    'host': '**************',
    'port': 3306,
    'database': 'antiFraudPlatform'
}

# 数据库连接池配置
ENGINE_CONFIG = {
    'pool_size': 10,  # 连接池大小
    'max_overflow': 20,  # 最大溢出连接数
    'pool_timeout': 30,  # 获取连接的超时时间
    'pool_recycle': 3600,  # 连接回收时间（秒）
    'pool_pre_ping': True  # 连接前测试连接是否有效
}

# 创建数据库引擎
engine: Engine = create_engine(URL.create(**DB_CONFIG), **ENGINE_CONFIG)

# 常量配置
FILE_PATH = '/Users/<USER>/Documents/日报数据/2025-07'
OPERATOR_KEY = '运营商(携号转网的以最终运营商为准)'
PROVINCE_KEY = '归属省'
DATE_RANGE = '2025年7月02日'
DATE_QUERY = '2025-07-02'

# 重点监测数据
INTENSIVE_MONITORING = "96106"

# 文件名称配置
WORD_NAME = f'{DATE_QUERY}案情分析日报.docx'
EXCEL_STATISTICS_NAME = '运营商与归属地查询导出任务——250702171601.xlsx'
EXCEL_CHECK_NAME = '20250702全国涉案警情.xlsx'

# 省级行政区（使用frozenset提高查找效率）
PROVINCES = frozenset({
    '北京市', '上海市', '天津市', '重庆市', '河北省', '山西省', '辽宁省',
    '吉林省', '黑龙江省', '江苏省', '浙江省', '安徽省', '福建省', '江西省',
    '山东省', '河南省', '湖北省', '湖南省', '广东省', '海南省', '四川省',
    '贵州省', '云南省', '陕西省', '甘肃省', '青海省', '台湾省', '内蒙古自治区',
    '广西壮族自治区', '西藏自治区', '宁夏回族自治区', '新疆维吾尔自治区',
    '香港特别行政区', '澳门特别行政区'
})

# 允许的文件扩展名
ALLOWED_EXTENSIONS = frozenset({'xlsx', 'xls'})


def query_sql(sql: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    安全的数据库查询函数
    Args:
        sql: SQL查询语句（支持参数化）
        params: 查询参数字典
    Returns:
        查询结果的字典列表
    """
    if not sql or not sql.strip():
        logger.warning("SQL语句为空")
        return []

    try:
        with engine.connect() as connection:
            result = connection.execute(text(sql), params or {})
            rows = result.fetchall()
            columns = result.keys()
            # 将数据转换成字典
            result_dict = [dict(zip(columns, row)) for row in rows]
            logger.info(f"查询成功，返回 {len(result_dict)} 条记录")
            return result_dict
    except SQLAlchemyError as e:
        logger.error(f"数据库查询错误: {e}")
        return []
    except Exception as e:
        logger.error(f"查询过程中发生未知错误: {e}")
        return []


def add(result: Dict[str, Any], sql: str) -> bool:
    """
    安全的数据库插入函数
    Args:
        result: 要插入的数据字典
        sql: SQL插入语句
    Returns:
        操作是否成功
    """
    if not sql or not sql.strip():
        logger.warning("SQL语句为空")
        return False

    if not result:
        logger.warning("插入数据为空")
        return False

    try:
        with engine.connect() as conn:
            conn.execute(text(sql), result)
            conn.commit()
            logger.info("数据插入成功")
            return True
    except SQLAlchemyError as e:
        logger.error(f"数据库插入错误: {e}")
        return False
    except Exception as e:
        logger.error(f"插入过程中发生未知错误: {e}")
        return False


def allowed_file(filename: str) -> bool:
    """
    检查文件扩展名是否允许
    Args:
        filename: 文件名
    Returns:
        是否允许该文件类型
    """
    if not filename:
        return False

    return ('.' in filename and
            filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS)


def test_connection() -> bool:
    """
    测试数据库连接
    Returns:
        连接是否成功
    """
    try:
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
            logger.info("数据库连接测试成功")
            return True
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        return False


# 使用示例
if __name__ == "__main__":
    # 测试连接
    if test_connection():
        print("数据库连接正常")
        
        # 查看数据库中的表
        tables_sql = "SHOW TABLES"
        tables = query_sql(tables_sql)
        print(f"数据库中的表: {[list(table.values())[0] for table in tables]}")
        
        # 查看主要业务表的结构
        main_tables = ['anti_fraud_case', 'anti_fraud_case_new', 'anti_fraud_case_new_2025_temp']
        for table_name in main_tables:
            if any(table_name in str(table.values()) for table in tables):
                print(f"\n=== 表 {table_name} 的结构 ===")
                desc_sql = f"DESC {table_name}"
                table_structure = query_sql(desc_sql)
                for field in table_structure:
                    print(f"  {field['Field']}: {field['Type']}")
                
                # 查看表的记录数
                count_sql = f"SELECT COUNT(*) as count FROM {table_name}"
                count_result = query_sql(count_sql)
                if count_result:
                    print(f"  总记录数: {count_result[0]['count']}")
                
                # 查看最近的几条记录（如果有日期字段）
                sample_sql = f"SELECT * FROM {table_name} LIMIT 3"
                sample_data = query_sql(sample_sql)
                if sample_data:
                    print(f"  示例数据字段: {list(sample_data[0].keys())}")
                break
    else:
        print("数据库连接失败")


def get_fraud_cases_by_date(date: str) -> List[Dict[str, Any]]:
    """
    根据日期查询反欺诈案例
    Args:
        date: 查询日期 (格式: YYYY-MM-DD)
    Returns:
        案例列表
    """
    # 这里需要根据实际的表结构和字段名来调整
    sql = """
    SELECT * FROM anti_fraud_case_new 
    WHERE DATE(create_time) = :query_date 
    ORDER BY create_time DESC
    """
    params = {"query_date": date}
    return query_sql(sql, params)


def get_daily_statistics(date: str) -> Dict[str, Any]:
    """
    获取指定日期的统计数据
    Args:
        date: 查询日期 (格式: YYYY-MM-DD)
    Returns:
        统计数据字典
    """
    sql = """
    SELECT 
        COUNT(*) as total_cases,
        COUNT(DISTINCT phone_number) as unique_phones
    FROM anti_fraud_case_new 
    WHERE DATE(create_time) = :query_date
    """
    params = {"query_date": date}
    result = query_sql(sql, params)
    return result[0] if result else {"total_cases": 0, "unique_phones": 0}