# -*- coding: utf-8 -*-
# @Time : 2025/2/19 17:20
# @software : PyCharm
# coding:utf-8
import re
import pandas as pd
import os
from util import file_path, excel_check_name, query_sql


class PhoneNumberExtractor:
    """电话号码提取器类"""

    def __init__(self):
        # 定义各种号码的正则模式和长度限制
        self.patterns = {
            '106': {
                'pattern_text': r'[^\d\s]106\d+',
                'pattern_phone': r'106\d+',
                'extract_pattern': r'106\d+',
                'length_check': lambda x: 11 <= len(x) <= 20
            },
            '96': {
                'pattern_text': r'[^\d\s]96\d+',
                'pattern_phone': None,
                'extract_pattern': r'96\d+',
                'length_check': lambda x: 10 < len(x) < 15
            },
            '096': {
                'pattern_text': r'[^\d\s]096\d+',
                'pattern_phone': None,
                'extract_pattern': r'096\d+',
                'length_check': lambda x: 10 < len(x) < 15
            },
            '0096': {
                'pattern_text': r'[^\d\s]0096\d+',
                'pattern_phone': None,
                'extract_pattern': r'0096\d+',
                'length_check': lambda x: 10 < len(x) < 15
            },
            '95': {
                'pattern_text': r'[^\d\s]95\d+',
                'pattern_phone': None,
                'extract_pattern': r'95\d+',
                'length_check': lambda x: len(x) == 7
            },
            '095': {
                'pattern_text': r'[^\d\s]095\d+',
                'pattern_phone': None,
                'extract_pattern': r'095\d+',
                'length_check': lambda x: 6 < len(x) <= 12
            },
            '0095': {
                'pattern_text': r'[^\d\s]0095\d+',
                'pattern_phone': None,
                'extract_pattern': r'0095\d+',
                'length_check': lambda x: 6 < len(x) <= 12
            },
            '00': {
                'pattern_text': r'[^\d\s]00\d+',
                'pattern_phone': None,
                'extract_pattern': r'00\d+',
                'length_check': lambda x: len(x) > 6
            },
            '400': {
                'pattern_text': r'[^\d\s]400\d+',
                'pattern_phone': None,
                'extract_pattern': r'400\d+',
                'length_check': lambda x: len(x) == 10
            }
        }

    def clean_plus_number(self, number):
        """清理+开头的号码，去除尾部的括号"""
        if len(number) <= 6:
            return number

        # 处理连续的括号
        while number.endswith(('（（', '））', '((', '))')):
            number = number[:-2]

        # 处理单个括号
        while number.endswith(('（', '）', '(', ')')):
            number = number[:-1]

        return number

    def extract_plus_numbers(self, text):
        """提取+开头的国际号码"""
        pattern_plus = r'\+(?!86)\d+[\d（）()\-]*'
        matches = re.findall(pattern_plus, text.replace(' ', ''))

        cleaned_numbers = []
        for match in matches:
            cleaned = self.clean_plus_number(match)
            if len(cleaned) > 6 and cleaned not in cleaned_numbers:
                cleaned_numbers.append(cleaned)

        return cleaned_numbers

    def extract_numbers_by_pattern(self, text, phone, pattern_key):
        """根据模式提取号码"""
        if pattern_key not in self.patterns:
            return []

        config = self.patterns[pattern_key]

        # 从案情描述中提取
        text_matches = re.findall(config['pattern_text'], text)

        # 从手机号字段中提取（仅106支持）
        phone_matches = []
        if config['pattern_phone'] and phone:
            phone_matches = re.findall(config['pattern_phone'], phone)

        all_matches = text_matches + phone_matches

        # 提取实际号码并去重
        extracted_numbers = []
        for match in all_matches:
            number = re.findall(config['extract_pattern'], match)[0]
            if config['length_check'](number) and number not in extracted_numbers:
                extracted_numbers.append(number)

        return extracted_numbers

    def process_single_record(self, record, text_field, phone_field):
        """处理单条记录，提取所有类型的号码"""
        text = str(record.get(text_field, ''))
        phone = str(record.get(phone_field, ''))

        all_numbers = []

        # 处理各种前缀的号码
        for pattern_key in self.patterns.keys():
            numbers = self.extract_numbers_by_pattern(text, phone, pattern_key)
            all_numbers.extend(numbers)

        # 处理+开头的号码
        plus_numbers = self.extract_plus_numbers(text)
        all_numbers.extend(plus_numbers)

        if all_numbers:
            # 去重并组合
            unique_numbers = list(dict.fromkeys(all_numbers))  # 保持顺序的去重
            record['正则匹配短信端口号'] = ','.join(unique_numbers)
            return record

        return None


def get_phone():
    """从Excel文件中提取电话号码"""
    extractor = PhoneNumberExtractor()

    try:
        # 读取Excel文件
        data = pd.read_excel(os.path.join(file_path, excel_check_name))

        # 按号码类型分类存储
        categorized_records = {
            '106': [],
            '96': [],
            '95': [],
            '00': [],
            'plus': [],
            '400': []
        }

        # 处理每条记录
        for _, row in data.iterrows():
            record = row.to_dict()
            processed_record = extractor.process_single_record(
                record, '简要案情', '嫌疑人手机号'
            )

            if processed_record:
                numbers = processed_record['正则匹配短信端口号'].split(',')

                # 根据号码类型分类
                for number in numbers:
                    if number.startswith('106'):
                        categorized_records['106'].append(processed_record.copy())
                    elif number.startswith(('96', '096', '0096')):
                        categorized_records['96'].append(processed_record.copy())
                    elif number.startswith(('95', '095', '0095')):
                        categorized_records['95'].append(processed_record.copy())
                    elif number.startswith('00'):
                        categorized_records['00'].append(processed_record.copy())
                    elif number.startswith('+'):
                        categorized_records['plus'].append(processed_record.copy())
                    elif number.startswith('400'):
                        categorized_records['400'].append(processed_record.copy())

        # 创建DataFrame并保存到Excel
        output_file = os.path.join(
            file_path,
            f"{excel_check_name.split('.')[0]}正则匹配短信端口号.xlsx"
        )

        with pd.ExcelWriter(output_file) as writer:
            for category, records in categorized_records.items():
                if records:
                    df = pd.DataFrame(records)
                    # 将正则匹配短信端口号列移到第二列
                    if '正则匹配短信端口号' in df.columns:
                        col = df.pop('正则匹配短信端口号')
                        df.insert(1, '正则匹配短信端口号', col)

                    sheet_names = {
                        '106': '106开头的手机号',
                        '96': '96开头的手机号',
                        '95': '95开头的手机号',
                        '00': '00开头的手机号',
                        'plus': '+开头的手机号',
                        '400': '400开头的手机号'
                    }

                    df.to_excel(writer, sheet_name=sheet_names[category], index=False)

        print(f"处理完成，结果保存到: {output_file}")

    except Exception as e:
        print(f"处理过程中出现错误: {e}")


def get_106_df(date_query: str):
    """从数据库中提取指定日期的106号码记录数量"""
    extractor = PhoneNumberExtractor()

    try:
        my_sql = f'''
            select *
            from anti_fraud_case_new afcnt 
            where insert_day = '{date_query}'
        '''
        data_list = query_sql(my_sql)

        count = 0
        for record in data_list:
            processed_record = extractor.process_single_record(
                record, 'brief_case_description', 'suspect_phone_number'
            )

            if processed_record:
                numbers = processed_record['正则匹配短信端口号'].split(',')
                # 检查是否包含106开头的号码
                if any(num.startswith('106') for num in numbers):
                    count += 1

        return count

    except Exception as e:
        print(f"查询数据库时出现错误: {e}")
        return 0


def get_106_df_month(month: str):
    """从数据库中提取指定月份的106号码记录数量"""
    extractor = PhoneNumberExtractor()

    try:
        my_sql = f'''
            select *
            from anti_fraud_case_new afcnt 
            where DATE_FORMAT(insert_day, '%Y-%m') = '{month}'
        '''
        data_list = query_sql(my_sql)

        count = 0
        for record in data_list:
            processed_record = extractor.process_single_record(
                record, 'brief_case_description', 'suspect_phone_number'
            )

            if processed_record:
                numbers = processed_record['正则匹配短信端口号'].split(',')
                # 检查是否包含106开头的号码
                if any(num.startswith('106') for num in numbers):
                    count += 1

        return count

    except Exception as e:
        print(f"查询数据库时出现错误: {e}")
        return 0


if __name__ == "__main__":
    get_phone()