#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化后的Ollama调用
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入修改后的类
from 测试_copy import TelecomMonitor

def test_ollama_simplified():
    """测试简化后的Ollama调用"""
    print("🧪 测试简化后的Ollama调用")
    print("=" * 50)
    
    # 创建监控实例
    monitor = TelecomMonitor()
    
    # 测试服务检查
    print("\n1. 测试服务检查:")
    service_ok, service_msg = monitor.check_ollama_service()
    print(f"   结果: {service_msg}")
    
    # 测试简单的API调用
    if service_ok:
        print("\n2. 测试API调用:")
        test_prompt = "请简单介绍一下电信诈骗的特点，不超过50字。"
        result = monitor.call_ollama_api(test_prompt)
        print(f"   提示词: {test_prompt}")
        print(f"   回复: {result}")
    else:
        print("\n2. 跳过API调用测试（服务不可用）")
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    test_ollama_simplified()
