#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试正则表达式APP名称过滤功能
"""

import pandas as pd
import re
from datetime import datetime, timedelta

def filter_invalid_app_names(df):
    """过滤无意义的APP名称 - 使用正则表达式优化"""
    if df is None or df.empty:
        return df

    # 显示过滤前的APP数量
    original_apps = df['app_name'].nunique()
    print(f"🔍 过滤前APP数量: {original_apps}")
    print(f"📋 过滤前APP列表: {sorted(df['app_name'].unique())}")

    # 创建APP名称的清理副本
    df_work = df.copy()
    df_work['app_name_clean'] = df_work['app_name'].str.strip()

    # 过滤条件
    mask = pd.Series(True, index=df_work.index)

    # 1. 过滤空值和空字符串
    mask &= df_work['app_name_clean'].notna()
    mask &= df_work['app_name_clean'] != ''

    # 2. 过滤纯数字（1-9999）
    mask &= ~df_work['app_name_clean'].str.match(r'^\d+$', na=False)

    # 3. 过滤问号模式（?、??、???、????等）
    mask &= ~df_work['app_name_clean'].str.match(r'^[?？]+$', na=False)

    # 4. 过滤星号模式（*、**、***等）
    mask &= ~df_work['app_name_clean'].str.match(r'^\*+$', na=False)

    # 5. 过滤井号模式（#、##、###等）
    mask &= ~df_work['app_name_clean'].str.match(r'^#+$', na=False)

    # 6. 过滤点号模式（.、..、...、。。。等）
    mask &= ~df_work['app_name_clean'].str.match(r'^[\.。]+$', na=False)

    # 7. 过滤横线模式（-、--、—、——等）
    mask &= ~df_work['app_name_clean'].str.match(r'^[-—]+$', na=False)

    # 8. 过滤斜线模式（/、//、\、\\等）
    mask &= ~df_work['app_name_clean'].str.match(r'^[/\\]+$', na=False)

    # 9. 过滤无意义词汇（不区分大小写）
    meaningless_patterns = [
        r'^无$', r'^wu$', r'^未知$', r'^unknown$', r'^null$', 
        r'^其他$', r'^其它$', r'^不详$', r'^不明$', r'^暂无$', r'^待定$',
        r'^test$', r'^demo$', r'^temp$', r'^临时$', r'^测试$',
        r'^韩文$', r'^日文$', r'^符号$', r'^特殊符号$',
        r'^受害人无法提供$', r'^teams?$'  # teams和team都匹配
    ]
    for pattern in meaningless_patterns:
        mask &= ~df_work['app_name_clean'].str.match(pattern, case=False, na=False)

    # 10. 过滤纯特殊符号组合（长度1-8，只包含特殊符号）
    mask &= ~df_work['app_name_clean'].str.match(r'^[^\w\u4e00-\u9fff]{1,8}$', na=False)

    # 11. 过滤长度过短的名称（小于2个字符，除非是知名APP）
    known_short_apps = ['QQ', 'UC', '58', '12306', '360', 'WPS']
    short_mask = (df_work['app_name_clean'].str.len() >= 2) | df_work['app_name_clean'].str.upper().isin([app.upper() for app in known_short_apps])
    mask &= short_mask

    # 12. 过滤混合无意义模式（数字+特殊符号，如：1-、2*、3?等）
    mask &= ~df_work['app_name_clean'].str.match(r'^\d+[^\w\u4e00-\u9fff]+$', na=False)
    mask &= ~df_work['app_name_clean'].str.match(r'^[^\w\u4e00-\u9fff]+\d+$', na=False)

    # 应用过滤条件
    filtered_df = df_work[mask].copy()
    
    # 移除工作列
    filtered_df = filtered_df.drop('app_name_clean', axis=1)

    # 显示过滤结果
    filtered_apps = filtered_df['app_name'].nunique()
    print(f"✅ 过滤后APP数量: {filtered_apps}")
    print(f"📋 过滤后APP列表: {sorted(filtered_df['app_name'].unique())}")

    if len(filtered_df) < len(df):
        removed_count = len(df) - len(filtered_df)
        print(f"🧹 已过滤 {removed_count} 条无效APP记录")

        # 显示被过滤的APP示例
        removed_apps = set(df['app_name'].unique()) - set(filtered_df['app_name'].unique())
        if removed_apps:
            sample_removed = sorted(list(removed_apps))
            print(f"🗑️ 被过滤的APP: {', '.join(sample_removed)}")

    return filtered_df

def normalize_app_names(df):
    """标准化APP名称 - 处理大小写和相似名称"""
    if df is None or df.empty:
        return df
        
    print(f"\n🔄 开始APP名称标准化...")
    print(f"📋 标准化前: {sorted(df['app_name'].unique())}")
    
    # 创建标准化映射
    normalization_map = {
        # 大小写标准化
        'teams': 'Teams',
        'TEAMS': 'Teams',
        'qq': 'QQ',
        'wechat': 'WeChat',
        'alipay': 'Alipay',
        'taobao': 'Taobao',
        # 相似名称合并
        '微信支付': '微信',
        'WeChat Pay': 'WeChat',
        '支付宝钱包': '支付宝',
        'Alipay Wallet': 'Alipay',
        # 英文中文对应
        'WeChat': '微信',
        'Alipay': '支付宝',
        'Taobao': '淘宝'
    }
    
    df_normalized = df.copy()
    
    # 应用标准化映射
    for old_name, new_name in normalization_map.items():
        mask = df_normalized['app_name'].str.lower() == old_name.lower()
        if mask.any():
            print(f"  📝 {old_name} → {new_name}")
        df_normalized.loc[mask, 'app_name'] = new_name
    
    print(f"📋 标准化后: {sorted(df_normalized['app_name'].unique())}")
    
    # 统计标准化效果
    original_count = df['app_name'].nunique()
    normalized_count = df_normalized['app_name'].nunique()
    
    if original_count != normalized_count:
        merged_count = original_count - normalized_count
        print(f"✅ APP名称标准化完成: 合并了 {merged_count} 个相似名称")
    
    return df_normalized

def create_test_data():
    """创建测试数据"""
    test_data = []
    
    # 有效的APP名称
    valid_apps = ['微信', '支付宝', '抖音', '淘宝', 'QQ', 'Teams', 'teams', 'TEAMS', 'WeChat', 'Alipay']
    
    # 无效的APP名称（各种模式）
    invalid_apps = [
        # 基础无意义词汇
        '无', 'wu', '未知', 'unknown', 'null', 'NULL',
        # 问号模式
        '?', '??', '???', '？', '？？', '？？？？',
        # 星号模式
        '*', '**', '***',
        # 井号模式
        '#', '##', '###',
        # 点号模式
        '.', '..', '...', '。。。',
        # 横线模式
        '-', '--', '—', '——',
        # 斜线模式
        '/', '//', '\\', '\\\\',
        # 纯数字
        '1', '2', '123', '9999',
        # 混合模式
        '1-', '2*', '3?', '*4', '?5',
        # 其他无意义词汇
        '其他', '不详', 'test', 'demo', '符号', '特殊符号', '受害人无法提供'
    ]
    
    all_apps = valid_apps + invalid_apps
    
    # 生成测试数据
    for i, app in enumerate(all_apps):
        test_data.append({
            'date_col': datetime.now() - timedelta(days=1),
            'app_name': app,
            'case_count': i + 1
        })
    
    return pd.DataFrame(test_data)

def main():
    """主函数"""
    print("🧪 正则表达式APP名称过滤测试")
    print("=" * 60)
    
    # 创建测试数据
    df = create_test_data()
    print(f"📊 测试数据创建完成，共 {len(df)} 条记录")
    
    # 执行过滤
    print("\n🔍 开始正则表达式过滤...")
    filtered_df = filter_invalid_app_names(df)
    
    # 执行标准化
    normalized_df = normalize_app_names(filtered_df)
    
    print(f"\n📈 最终结果:")
    print(f"  - 原始记录数: {len(df)}")
    print(f"  - 过滤后记录数: {len(filtered_df)}")
    print(f"  - 标准化后记录数: {len(normalized_df)}")
    print(f"  - 过滤效率: {(len(df) - len(filtered_df)) / len(df) * 100:.1f}%")
    print(f"  - 最终APP数量: {normalized_df['app_name'].nunique()}")

if __name__ == "__main__":
    main()
