#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复缓存和过滤问题的脚本
"""

import os
from pathlib import Path

def clear_cache():
    """清除缓存文件"""
    cache_dir = Path("../cache")
    cache_file = cache_dir / "app_fraud_cache.json"
    
    try:
        if cache_file.exists():
            cache_file.unlink()
            print("✅ 缓存文件已删除")
        else:
            print("ℹ️ 缓存文件不存在")
            
        # 也检查当前目录的缓存
        current_cache = Path("cache/app_fraud_cache.json")
        if current_cache.exists():
            current_cache.unlink()
            print("✅ 当前目录缓存文件已删除")
            
    except Exception as e:
        print(f"❌ 删除缓存失败: {e}")

def main():
    """主函数"""
    print("🔧 修复缓存和过滤问题")
    print("=" * 40)
    
    # 清除缓存
    print("1. 清除缓存文件...")
    clear_cache()
    
    print("\n📋 修复说明:")
    print("1. ✅ 已清除缓存文件")
    print("2. ✅ 修复了日期格式问题")
    print("3. ✅ 增强了过滤功能显示")
    print("4. ✅ 添加了过滤结果统计")
    
    print("\n🎯 下一步操作:")
    print("1. 运行 TopApp.py")
    print("2. 选择菜单选项 6 (清除缓存)")
    print("3. 然后选择任意图表选项测试")
    print("4. 观察过滤功能是否正常工作")
    
    print("\n💡 如果仍然看到无效APP名称:")
    print("1. 检查数据库中的实际数据")
    print("2. 可能需要调整过滤规则")
    print("3. 或者数据库中确实存在这些名称")

if __name__ == "__main__":
    main()
