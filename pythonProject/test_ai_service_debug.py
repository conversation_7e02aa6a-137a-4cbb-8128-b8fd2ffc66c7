#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试AI服务初始化问题
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.idea/Ready/test/50万趋势'))

def test_prompt_manager():
    """测试提示词管理器"""
    print("🧪 测试提示词管理器")
    print("=" * 50)
    
    try:
        from prompt_manager import prompt_manager
        print("✅ 提示词管理器导入成功")
        
        # 获取所有模板
        templates = prompt_manager.get_all_templates()
        print(f"✅ 获取到 {len(templates)} 个模板:")
        for key, template in templates.items():
            print(f"  - {key}: {template.name}")
            
        return True
        
    except Exception as e:
        print(f"❌ 提示词管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_service():
    """测试AI服务"""
    print("\n🧪 测试AI服务")
    print("=" * 50)
    
    try:
        from ai_service import AIService
        print("✅ AI服务导入成功")
        
        # 创建AI服务实例
        ai_service = AIService()
        print("✅ AI服务实例创建成功")
        
        # 测试get_available_analyses方法
        if hasattr(ai_service, 'get_available_analyses'):
            print("✅ get_available_analyses 方法存在")
            
            analyses = ai_service.get_available_analyses()
            print(f"✅ 获取到 {len(analyses)} 个可用分析:")
            for key, name in analyses.items():
                print(f"  - {key}: {name}")
                
        else:
            print("❌ get_available_analyses 方法不存在")
            
        return True
        
    except Exception as e:
        print(f"❌ AI服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_manager():
    """测试配置管理器"""
    print("\n🧪 测试配置管理器")
    print("=" * 50)
    
    try:
        from config_manager import config
        print("✅ 配置管理器导入成功")
        
        ai_config = config.get_ai_config()
        print(f"✅ AI配置获取成功: {ai_config}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_progress_bar():
    """测试新的进度条"""
    print("\n🧪 测试新的进度条")
    print("=" * 50)
    
    try:
        from ai_service import AIAnalysisProgressBar
        print("✅ 进度条类导入成功")
        
        # 创建进度条实例
        progress_bar = AIAnalysisProgressBar()
        print("✅ 进度条实例创建成功")
        
        # 测试基本功能
        progress_bar.start(731)
        print("✅ 进度条启动成功")
        
        # 显示一次进度
        progress_bar.display_progress()
        print("✅ 进度显示成功")
        
        # 完成进度条
        progress_bar.complete()
        print("✅ 进度条完成成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 进度条测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 AI服务调试测试套件")
    print("=" * 60)
    
    # 检查工作目录
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")
    
    # 检查文件是否存在
    files_to_check = [
        '.idea/Ready/test/50万趋势/ai_service.py',
        '.idea/Ready/test/50万趋势/prompt_manager.py',
        '.idea/Ready/test/50万趋势/config_manager.py',
        '.idea/Ready/test/50万趋势/prompts.yaml',
        '.idea/Ready/test/50万趋势/config.yaml'
    ]
    
    print("\n📁 检查必要文件:")
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (文件不存在)")
    
    # 运行测试
    tests = [
        ("配置管理器", test_config_manager),
        ("提示词管理器", test_prompt_manager),
        ("AI服务", test_ai_service),
        ("进度条", test_progress_bar),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} 测试 {'='*20}")
        results[test_name] = test_func()
    
    # 总结结果
    print(f"\n{'='*60}")
    print("🏁 测试结果总结:")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    if all_passed:
        print("\n🎉 所有测试通过！AI服务应该可以正常工作。")
    else:
        print("\n⚠️ 部分测试失败，需要修复问题。")

if __name__ == "__main__":
    main()
