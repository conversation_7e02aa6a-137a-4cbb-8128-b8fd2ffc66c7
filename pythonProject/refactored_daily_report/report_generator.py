"""
报告生成模块
负责将数据转换为Word文档格式的报告
"""
import logging
import os
from typing import Dict, Any, Optional
from io import BytesIO
from datetime import datetime

try:
    from docx import Document
    from docx.shared import Pt, Cm, RGBColor
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.oxml.ns import qn
except ImportError:
    logging.warning("python-docx 未安装，报告生成功能将不可用")
    Document = None

from .models import ReportData
from .config import config

logger = logging.getLogger(__name__)


class ReportTemplate:
    """报告模板类"""
    
    @staticmethod
    def setup_document_styles(doc):
        """设置文档样式"""
        if not doc:
            return
        
        # 设置默认样式（正文）
        style = doc.styles['Normal']
        font = style.font
        font.name = '仿宋_GB2312'
        font.size = Pt(12)  # 3号字体大小
        font.color.rgb = RGBColor(0, 0, 0)
        style._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')
        style.paragraph_format.first_line_indent = Cm(0.67)
        
        # 设置标题样式
        for heading_style in ['Heading 1', 'Heading 2']:
            h_style = doc.styles[heading_style]
            h_font = h_style.font
            h_font.name = '黑体'
            h_font.color.rgb = RGBColor(0, 0, 0)
            h_style._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
    
    @staticmethod
    def add_title(doc, title: str = "反诈骗案情分析日报"):
        """添加标题"""
        if not doc:
            return
        
        title_paragraph = doc.add_heading(title, 0)
        title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 设置标题样式
        title_run = title_paragraph.runs[0]
        title_run.font.name = '黑体'
        title_run.font.size = Pt(18)
        title_run.font.color.rgb = RGBColor(0, 0, 0)
        title_run._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
    
    @staticmethod
    def add_section_title(doc, title: str, level: int = 1):
        """添加章节标题"""
        if not doc:
            return
        
        heading = doc.add_heading(title, level)
        heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
        return heading
    
    @staticmethod
    def add_paragraph(doc, text: str, indent: bool = True):
        """添加段落"""
        if not doc:
            return
        
        paragraph = doc.add_paragraph(text)
        if indent:
            paragraph.paragraph_format.first_line_indent = Cm(0.67)
        return paragraph
    
    @staticmethod
    def add_table(doc, headers: list, data: list):
        """添加表格"""
        if not doc or not headers or not data:
            return
        
        table = doc.add_table(rows=1, cols=len(headers))
        table.style = 'Table Grid'
        
        # 添加表头
        hdr_cells = table.rows[0].cells
        for i, header in enumerate(headers):
            hdr_cells[i].text = header
        
        # 添加数据行
        for row_data in data:
            row_cells = table.add_row().cells
            for i, cell_data in enumerate(row_data):
                if i < len(row_cells):
                    row_cells[i].text = str(cell_data)
        
        return table


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self):
        self.template = ReportTemplate()
        
        if Document is None:
            raise ImportError("python-docx 未安装，无法生成Word报告")
    
    def generate_overall_situation_section(self, doc, report_data: ReportData):
        """生成案情总体形势部分"""
        self.template.add_section_title(doc, "一、案情总体形势")
        
        # 基础统计信息
        stats = report_data.case_stats
        
        situation_text = (
            f"本日案情{stats.total_count}条，涉案金额{stats.total_amount}亿元。"
            f"其中URL类涉案{stats.url_count}起，占比{stats.url_percentage}%；"
            f"APP类涉案{stats.app_count}起，占比{stats.app_percentage}%；"
            f"电话、短信引流{stats.sms_count}起，占比{stats.sms_percentage}%；"
            f"国际号码涉案{stats.international_count}起。"
        )
        
        # 添加运营商信息
        operator_summary = report_data.get_operator_summary()
        if operator_summary:
            situation_text += f"运营商分布：{operator_summary}。"
        
        # 添加日环比信息
        if report_data.daily_trend.change_description:
            situation_text += f"较昨日{report_data.daily_trend.change_description}%。"
        
        self.template.add_paragraph(doc, situation_text)
    
    def generate_case_distribution_section(self, doc, report_data: ReportData):
        """生成案件分布情况部分"""
        self.template.add_section_title(doc, "二、案件分布情况")
        
        # 案件类型分布
        if report_data.case_types:
            case_type_summary = report_data.get_case_type_summary()
            distribution_text = f"案件类型分布：{case_type_summary}。"
            self.template.add_paragraph(doc, distribution_text)
            
            # 添加案件类型表格
            if len(report_data.case_types) > 5:
                headers = ["排名", "案件类型", "案件数量", "涉案金额(万元)", "占比(%)"]
                table_data = []
                for i, case_type in enumerate(report_data.case_types[:10], 1):
                    table_data.append([
                        i,
                        case_type.case_type,
                        case_type.total_count,
                        case_type.amount,
                        case_type.percentage
                    ])
                
                self.template.add_table(doc, headers, table_data)
    
    def generate_app_analysis_section(self, doc, report_data: ReportData):
        """生成APP涉案分析部分"""
        self.template.add_section_title(doc, "三、APP涉案分析")
        
        # APP基础统计
        app_text = (
            f"本日APP类案件{report_data.case_stats.app_count}起，"
            f"涉及APP总数{report_data.app_total_count}个，"
            f"涉案金额{report_data.app_total_amount}万元。"
        )
        
        if report_data.new_app_count > 0:
            app_text += f"新增APP{report_data.new_app_count}个。"
        
        self.template.add_paragraph(doc, app_text)
        
        # TOP APP列表
        if report_data.app_stats:
            app_summary = report_data.get_app_summary()
            if app_summary:
                self.template.add_paragraph(doc, f"TOP5 APP：{app_summary}。")
            
            # 新增APP详情
            new_app_summary = report_data.get_new_app_summary()
            if new_app_summary:
                self.template.add_paragraph(doc, new_app_summary)
            
            # 添加APP统计表格
            if len(report_data.app_stats) > 5:
                headers = ["排名", "APP名称", "案件数量", "占比(%)", "是否新增", "累计案件"]
                table_data = []
                for i, app in enumerate(report_data.app_stats[:20], 1):
                    table_data.append([
                        i,
                        app.app_name,
                        app.total_count,
                        app.percentage,
                        "是" if app.is_new else "否",
                        app.accumulate_count
                    ])
                
                self.template.add_table(doc, headers, table_data)
    
    def generate_operator_analysis_section(self, doc, report_data: ReportData):
        """生成运营商分析部分"""
        self.template.add_section_title(doc, "四、运营商涉案分析")
        
        if not report_data.operators:
            self.template.add_paragraph(doc, "暂无运营商数据。")
            return
        
        # 运营商统计表格
        headers = ["运营商", "案件数量", "涉案金额(万元)", "占比(%)", "50万以上", "100万以上", "1000万以上"]
        table_data = []
        
        for name, stats in report_data.operators.items():
            table_data.append([
                name,
                stats.total_count,
                stats.total_amount,
                stats.percentage,
                stats.high_amount_50,
                stats.high_amount_100,
                stats.high_amount_1000
            ])
        
        self.template.add_table(doc, headers, table_data)
        
        # 电信专项分析
        if "电信" in report_data.operators:
            telecom_stats = report_data.operators["电信"]
            telecom_text = (
                f"中国电信手机号码涉案{telecom_stats.total_count}起，"
                f"涉案金额{telecom_stats.total_amount}万元，"
                f"占比{telecom_stats.percentage}%。"
            )
            
            if report_data.telecom_trend.change_description:
                telecom_text += f"较昨日{report_data.telecom_trend.change_description}%。"
            
            if telecom_stats.high_amount_50 > 0 or telecom_stats.high_amount_100 > 0 or telecom_stats.high_amount_1000 > 0:
                telecom_text += (
                    f"其中50万以上案件{telecom_stats.high_amount_50}个，"
                    f"100万以上案件{telecom_stats.high_amount_100}个，"
                    f"1000万以上案件{telecom_stats.high_amount_1000}个。"
                )
            
            self.template.add_paragraph(doc, telecom_text)
    
    def generate_monitoring_section(self, doc, report_data: ReportData):
        """生成重点监测部分"""
        self.template.add_section_title(doc, "五、重点监测")
        
        monitoring_text = (
            f"重点监测号码段：{report_data.monitoring.monitoring_numbers}，"
            f"涉案{report_data.monitoring.case_count}起。"
        )
        
        self.template.add_paragraph(doc, monitoring_text)
    
    def generate_high_amount_section(self, doc, report_data: ReportData):
        """生成高金额案件部分"""
        self.template.add_section_title(doc, "六、高金额案件分析")
        
        high_amount_text = (
            f"50万以上案件{report_data.high_amount_50_count}起，"
            f"其中有运营商信息的{report_data.high_amount_50_operator_count}起，"
            f"电信号码{report_data.high_amount_50_telecom_count}起。"
        )
        
        self.template.add_paragraph(doc, high_amount_text)
        
        # 1000万以上案件编号
        if report_data.high_amount_cases:
            case_numbers_text = f"1000万以上案件编号：{', '.join(report_data.high_amount_cases[:10])}。"
            self.template.add_paragraph(doc, case_numbers_text)
    
    def generate_trend_section(self, doc, report_data: ReportData):
        """生成趋势分析部分"""
        self.template.add_section_title(doc, "七、趋势分析")
        
        # 日环比
        daily_text = (
            f"日环比：本日案件{report_data.daily_trend.current_count}起，"
            f"昨日{report_data.daily_trend.previous_count}起，"
            f"{report_data.daily_trend.change_description}%。"
        )
        self.template.add_paragraph(doc, daily_text)
        
        # 月环比
        monthly_text = (
            f"月环比：本月累计{report_data.monthly_trend.current_count}起，"
            f"上月同期{report_data.monthly_trend.previous_count}起，"
            f"{report_data.monthly_trend.change_description}%。"
        )
        self.template.add_paragraph(doc, monthly_text)
        
        # 月度电信统计
        if report_data.monthly_telecom_count > 0:
            monthly_telecom_text = (
                f"本月电信累计案件{report_data.monthly_telecom_count}起，"
                f"占本月总案件的{report_data.monthly_telecom_percentage}%。"
            )
            self.template.add_paragraph(doc, monthly_telecom_text)
    
    def generate_report(self, report_data: ReportData, output_path: Optional[str] = None) -> BytesIO:
        """
        生成完整报告
        
        Args:
            report_data: 报告数据
            output_path: 输出文件路径，如果为None则返回BytesIO对象
            
        Returns:
            BytesIO: 文档流对象
        """
        logger.info("开始生成Word报告")
        
        try:
            # 创建文档
            doc = Document()
            
            # 设置文档样式
            self.template.setup_document_styles(doc)
            
            # 添加标题
            title = f"{report_data.date_range}反诈骗案情分析日报"
            self.template.add_title(doc, title)
            
            # 生成各个部分
            self.generate_overall_situation_section(doc, report_data)
            self.generate_case_distribution_section(doc, report_data)
            self.generate_app_analysis_section(doc, report_data)
            self.generate_operator_analysis_section(doc, report_data)
            self.generate_monitoring_section(doc, report_data)
            self.generate_high_amount_section(doc, report_data)
            self.generate_trend_section(doc, report_data)
            
            # 保存文档
            if output_path:
                # 确保输出目录存在
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                doc.save(output_path)
                logger.info(f"报告已保存到: {output_path}")
                
                # 同时返回BytesIO对象
                file_stream = BytesIO()
                doc.save(file_stream)
                file_stream.seek(0)
                return file_stream
            else:
                # 只返回BytesIO对象
                file_stream = BytesIO()
                doc.save(file_stream)
                file_stream.seek(0)
                logger.info("报告生成完成")
                return file_stream
                
        except Exception as e:
            logger.error(f"生成报告时发生错误: {e}")
            raise
    
    def generate_simple_report(self, report_data: ReportData) -> str:
        """
        生成简单的文本报告
        
        Args:
            report_data: 报告数据
            
        Returns:
            str: 文本报告内容
        """
        logger.info("生成简单文本报告")
        
        try:
            lines = []
            lines.append(f"{report_data.date_range}反诈骗案情分析日报")
            lines.append("=" * 50)
            
            # 基础统计
            stats = report_data.case_stats
            lines.append(f"案件总数: {stats.total_count}")
            lines.append(f"涉案金额: {stats.total_amount}亿元")
            lines.append(f"URL案件: {stats.url_count}起 ({stats.url_percentage}%)")
            lines.append(f"APP案件: {stats.app_count}起 ({stats.app_percentage}%)")
            lines.append(f"短信案件: {stats.sms_count}起 ({stats.sms_percentage}%)")
            lines.append(f"国际号码: {stats.international_count}起")
            lines.append("")
            
            # 运营商统计
            if report_data.operators:
                lines.append("运营商统计:")
                for name, stats in report_data.operators.items():
                    lines.append(f"  {name}: {stats.total_count}起 ({stats.percentage}%) {stats.total_amount}万元")
                lines.append("")
            
            # 趋势分析
            lines.append("趋势分析:")
            lines.append(f"  日环比: {report_data.daily_trend.change_description}%")
            lines.append(f"  月环比: {report_data.monthly_trend.change_description}%")
            lines.append("")
            
            # 重点监测
            lines.append(f"重点监测: {report_data.monitoring.monitoring_numbers} ({report_data.monitoring.case_count}起)")
            
            report_text = "\n".join(lines)
            logger.info("简单文本报告生成完成")
            return report_text
            
        except Exception as e:
            logger.error(f"生成简单报告时发生错误: {e}")
            return f"报告生成失败: {e}"


def create_report_generator() -> ReportGenerator:
    """
    创建报告生成器的工厂函数
    
    Returns:
        ReportGenerator: 报告生成器实例
    """
    return ReportGenerator()
