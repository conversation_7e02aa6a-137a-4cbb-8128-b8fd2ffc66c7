"""
数据模型定义
定义报告生成过程中使用的数据结构
"""
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime


@dataclass
class CaseStatistics:
    """案件统计数据"""
    total_count: int = 0
    total_amount: float = 0.0
    url_count: int = 0
    url_percentage: float = 0.0
    app_count: int = 0
    app_percentage: float = 0.0
    sms_count: int = 0
    sms_percentage: float = 0.0
    international_count: int = 0


@dataclass
class OperatorStatistics:
    """运营商统计数据"""
    name: str
    total_count: int = 0
    total_amount: float = 0.0
    high_amount_50: int = 0    # 50万以上案件数
    high_amount_100: int = 0   # 100万以上案件数
    high_amount_1000: int = 0  # 1000万以上案件数
    percentage: float = 0.0
    
    def calculate_percentage(self, total: int):
        """计算占比"""
        if total > 0:
            self.percentage = round((self.total_count / total) * 100, 2)


@dataclass
class AppStatistics:
    """APP统计数据"""
    app_name: str
    total_count: int = 0
    amount: float = 0.0
    percentage: float = 0.0
    is_new: bool = False
    accumulate_count: int = 0  # 累计数量


@dataclass
class CaseTypeStatistics:
    """案件类型统计"""
    case_type: str
    total_count: int = 0
    amount: float = 0.0
    percentage: float = 0.0


@dataclass
class PhoneLocationInfo:
    """手机号归属地信息"""
    phone_info: str
    amount_range: str  # 金额范围：50-100万、100-1000万、1000万以上


@dataclass
class TrendData:
    """趋势数据"""
    current_count: int = 0
    previous_count: int = 0
    change_percentage: float = 0.0
    change_description: str = ""
    
    def calculate_trend(self):
        """计算趋势"""
        if self.previous_count > 0:
            self.change_percentage = round(
                (self.current_count - self.previous_count) / self.previous_count * 100, 2
            )
        else:
            self.change_percentage = 100.0 if self.current_count > 0 else 0.0
        
        if self.change_percentage < 0:
            self.change_description = f"降低{abs(self.change_percentage):.2f}"
        else:
            self.change_description = f"增加{self.change_percentage:.2f}"


@dataclass
class ChartData:
    """图表数据"""
    x_data: List[str] = field(default_factory=list)  # X轴数据（日期）
    y_data: List[int] = field(default_factory=list)  # Y轴数据（数量）
    label: str = ""  # 数据标签


@dataclass
class MonitoringData:
    """重点监测数据"""
    monitoring_numbers: str = ""  # 监测号码
    case_count: int = 0  # 涉案数量


@dataclass
class ReportData:
    """完整的报告数据"""
    # 基本信息
    date_range: str = ""
    date_query: str = ""
    
    # 案件统计
    case_stats: CaseStatistics = field(default_factory=CaseStatistics)
    
    # 运营商统计
    operators: Dict[str, OperatorStatistics] = field(default_factory=dict)
    
    # APP统计
    app_stats: List[AppStatistics] = field(default_factory=list)
    app_total_count: int = 0
    app_total_amount: float = 0.0
    new_app_count: int = 0
    
    # 案件类型统计
    case_types: List[CaseTypeStatistics] = field(default_factory=list)
    
    # 手机号归属地信息
    phone_locations: Dict[str, List[PhoneLocationInfo]] = field(default_factory=dict)
    
    # 趋势数据
    daily_trend: TrendData = field(default_factory=TrendData)
    monthly_trend: TrendData = field(default_factory=TrendData)
    telecom_trend: TrendData = field(default_factory=TrendData)
    
    # 图表数据
    chart_data: Dict[str, ChartData] = field(default_factory=dict)
    
    # 重点监测
    monitoring: MonitoringData = field(default_factory=MonitoringData)
    
    # 高金额案件
    high_amount_cases: List[str] = field(default_factory=list)  # 案件编号列表
    high_amount_50_count: int = 0
    high_amount_50_operator_count: int = 0
    high_amount_50_telecom_count: int = 0
    
    # 月度累计数据
    monthly_total_count: int = 0
    monthly_telecom_count: int = 0
    monthly_telecom_percentage: float = 0.0
    
    def get_operator_summary(self) -> str:
        """获取运营商占比摘要"""
        if not self.operators:
            return ""
        
        total = sum(op.total_count for op in self.operators.values())
        if total == 0:
            return ""
        
        # 更新各运营商占比
        for operator in self.operators.values():
            operator.calculate_percentage(total)
        
        # 生成摘要文本
        summary_parts = []
        for name, stats in self.operators.items():
            if stats.total_count > 0:
                summary_parts.append(f"{name}{stats.total_count}起(占比{stats.percentage}%)")
        
        return "、".join(summary_parts)
    
    def get_case_type_summary(self) -> str:
        """获取案件类型分布摘要"""
        if not self.case_types:
            return ""
        
        # 取前5个类型
        top_5 = self.case_types[:5]
        return "、".join([f"{item.case_type}占比{item.percentage}%" for item in top_5])
    
    def get_app_summary(self) -> str:
        """获取APP摘要"""
        if not self.app_stats:
            return ""
        
        # 取前5个APP
        top_5 = self.app_stats[:5]
        return "、".join([f"{item.app_name}" for item in top_5])
    
    def get_new_app_summary(self) -> str:
        """获取新增APP摘要"""
        new_apps = [app for app in self.app_stats if app.is_new]
        if not new_apps:
            return ""
        
        # 取前10个新增APP
        top_10 = new_apps[:10]
        summary_parts = []
        for app in top_10:
            summary_parts.append(
                f""{app.app_name}"，涉案{app.total_count}起(占比{app.percentage}%)，涉案金额{app.amount}万元"
            )
        
        return "其中新增涉诈APP：" + "；".join(summary_parts)


@dataclass
class QueryResult:
    """查询结果封装"""
    data: List[Dict[str, Any]] = field(default_factory=list)
    success: bool = True
    error_message: str = ""
    query_time: Optional[datetime] = None
    
    def __post_init__(self):
        if self.query_time is None:
            self.query_time = datetime.now()
    
    def is_empty(self) -> bool:
        """检查结果是否为空"""
        return not self.data or len(self.data) == 0
    
    def get_first(self) -> Optional[Dict[str, Any]]:
        """获取第一条记录"""
        return self.data[0] if self.data else None
    
    def get_value(self, key: str, default: Any = None) -> Any:
        """从第一条记录中获取指定字段的值"""
        first = self.get_first()
        return first.get(key, default) if first else default
