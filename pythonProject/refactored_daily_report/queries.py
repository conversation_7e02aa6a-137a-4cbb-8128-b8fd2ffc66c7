"""
SQL查询管理模块
使用模板化的方式管理所有SQL查询
"""
from typing import Dict, List, Any
from string import Template


class QueryTemplates:
    """SQL查询模板类"""
    
    # 基础统计查询
    BASIC_STATS = {
        "total_cases": Template("""
            SELECT COUNT(*) as case_count, 
                   ROUND(SUM(involved_amount)/100000000, 2) as total_amount 
            FROM anti_fraud_case_new 
            WHERE insert_day = '$date'
        """),
        
        "url_cases": Template("""
            SELECT COUNT(*) as total_count, 
                   ROUND(COUNT(*) / (SELECT COUNT(1) FROM anti_fraud_case_new WHERE insert_day = '$date') * 100, 2) as percentage 
            FROM anti_fraud_case_new  
            WHERE url != '无' AND insert_day = '$date'
        """),
        
        "app_cases": Template("""
            SELECT COUNT(*) as total_count, 
                   ROUND(COUNT(*) / (SELECT COUNT(1) FROM anti_fraud_case_new WHERE insert_day = '$date') * 100, 2) as percentage 
            FROM anti_fraud_case_new 
            WHERE app_name != '无' AND app_name != '' AND insert_day = '$date'
        """),
        
        "sms_cases": Template("""
            SELECT COUNT(*) as total_count,
                   ROUND(COUNT(*) / (SELECT COUNT(1) FROM anti_fraud_case_new WHERE insert_day = '$date') * 100, 2) as percentage  
            FROM anti_fraud_case_new  
            WHERE insert_day = '$date' AND (connection_relationship != '无' OR suspect_phone_number != '')
        """),
        
        "international_cases": Template("""
            SELECT COUNT(*) as tel_count 
            FROM anti_fraud_case_new 
            WHERE suspect_phone_number != '' 
              AND suspect_phone_number REGEXP '^(00|\\+)' 
              AND suspect_phone_number NOT REGEXP '^(0086|\\+86)' 
              AND insert_day = '$date'
        """)
    }
    
    # 案件类型统计
    CASE_TYPE_STATS = Template("""
        SELECT @row_number:=@row_number + 1 AS id,
               sorted.* 
        FROM (
            SELECT case_main_type as type,
                   COUNT(*) as total_count,
                   ROUND(SUM(involved_amount)/10000, 2) as amount, 
                   ROUND(COUNT(*) / (SELECT COUNT(*) FROM anti_fraud_case_new WHERE insert_day = '$date') * 100, 2) as percentage 
            FROM anti_fraud_case_new 
            WHERE insert_day = '$date' 
            GROUP BY case_main_type 
            ORDER BY total_count DESC 
        ) AS sorted,
        (SELECT @row_number:=0) AS init
    """)
    
    # APP统计查询
    APP_STATS = {
        "top_apps": Template("""
            SELECT @row_number:=@row_number + 1 AS id,  
                   sorted.* 
            FROM (   
                SELECT app_name, 
                       COUNT(*) as total_count, 
                       ROUND(COUNT(*) / (SELECT COUNT(*) FROM anti_fraud_case_new 
                                       WHERE insert_day = '$date' 
                                         AND app_name NOT IN $excluded_apps) * 100, 2) as percentage 
                FROM anti_fraud_case_new  
                WHERE app_name NOT IN $excluded_apps 
                  AND insert_day = '$date' 
                GROUP BY app_name 
                ORDER BY total_count DESC 
                LIMIT 20  
            ) AS sorted,
            (SELECT @row_number:=0) AS init
        """),
        
        "app_total": Template("""
            SELECT COUNT(1) as app_total 
            FROM anti_fraud_case_new  
            WHERE app_name NOT IN $excluded_apps 
              AND insert_day = '$date'
        """),
        
        "app_amount_stats": Template("""
            SELECT IFNULL(ROUND(SUM(amount) / 10000, 2), 0) as amount,
                   COUNT(1) as total_count 
            FROM (
                SELECT SUM(involved_amount) as amount,
                       COUNT(1) as appnum 
                FROM anti_fraud_case_new  
                WHERE app_name NOT IN $excluded_apps 
                  AND insert_day = '$date' 
                GROUP BY app_name 
            ) as a
        """),
        
        "app_detail_stats": Template("""
            SELECT IFNULL(ROUND(SUM(involved_amount) / 10000, 2), 0) as amount,
                   app_name,
                   COUNT(1) as appnum 
            FROM anti_fraud_case_new  
            WHERE app_name NOT IN $excluded_apps 
              AND insert_day = '$date' 
            GROUP BY app_name
        """)
    }
    
    # 运营商统计查询
    OPERATOR_STATS = {
        "operator_basic": Template("""
            SELECT COUNT(1) as total_count, 
                   IFNULL(ROUND(SUM(involved_amount) / 10000, 2), 0) as total_money 
            FROM anti_fraud_case_new 
            WHERE suspect_phone_info LIKE '%$operator%' 
              AND insert_day = '$date'
        """),
        
        "operator_high_amount": Template("""
            SELECT COUNT(*) as total_count,
                   SUM(involved_amount) as total_amount
            FROM anti_fraud_case_new 
            WHERE involved_amount >= $min_amount 
              AND involved_amount < $max_amount 
              AND insert_day = '$date' 
              AND suspect_phone_info LIKE '%$operator%'
        """),
        
        "operator_highest_amount": Template("""
            SELECT COUNT(*) as total_count,
                   SUM(involved_amount) as total_amount
            FROM anti_fraud_case_new 
            WHERE involved_amount >= $min_amount 
              AND insert_day = '$date' 
              AND suspect_phone_info LIKE '%$operator%'
        """)
    }
    
    # 手机号归属地查询
    PHONE_LOCATION_QUERIES = {
        "location_by_amount": Template("""
            SELECT suspect_phone_info 
            FROM anti_fraud_case_new 
            WHERE involved_amount >= $min_amount 
              AND involved_amount < $max_amount 
              AND insert_day = '$date' 
              AND suspect_phone_number REGEXP '$phone_pattern'
        """),
        
        "location_highest_amount": Template("""
            SELECT suspect_phone_info 
            FROM anti_fraud_case_new 
            WHERE involved_amount >= $min_amount 
              AND insert_day = '$date' 
              AND suspect_phone_number REGEXP '$phone_pattern'
        """)
    }
    
    # 趋势分析查询
    TREND_QUERIES = {
        "daily_comparison": Template("""
            SELECT COUNT(*) as case_count 
            FROM anti_fraud_case_new 
            WHERE STR_TO_DATE(insert_day, '%Y-%m-%d') = DATE_FORMAT('$date', '%Y-%m-%d')
        """),
        
        "previous_day": Template("""
            SELECT COUNT(*) as case_count 
            FROM anti_fraud_case_new 
            WHERE STR_TO_DATE(insert_day, '%Y-%m-%d') = DATE_FORMAT(DATE_SUB('$date', INTERVAL 1 DAY), '%Y-%m-%d')
        """),
        
        "monthly_comparison": Template("""
            SELECT COUNT(*) as case_count 
            FROM anti_fraud_case_new 
            WHERE DATE_FORMAT(STR_TO_DATE(insert_day, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT('$date', '%Y-%m')
        """),
        
        "previous_month": Template("""
            SELECT COUNT(*) as case_count 
            FROM anti_fraud_case_new 
            WHERE DATE_FORMAT(STR_TO_DATE(insert_day, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT(DATE_SUB('$date', INTERVAL 1 MONTH), '%Y-%m')
        """),
        
        "telecom_yesterday": Template("""
            SELECT COUNT(1) as total_count  
            FROM anti_fraud_case_new 
            WHERE suspect_phone_info LIKE '%电信%' 
              AND STR_TO_DATE(insert_day, '%Y-%m-%d') = DATE_FORMAT(DATE_SUB('$date', INTERVAL 1 DAY), '%Y-%m-%d')
        """)
    }
    
    # 重点监测查询
    MONITORING_QUERIES = {
        "monitoring_cases": Template("""
            SELECT COUNT(*) as total_count 
            FROM anti_fraud_case_new 
            WHERE insert_day = '$date' 
              AND suspect_phone_number REGEXP '$monitoring_pattern'
        """)
    }
    
    # 高金额案件查询
    HIGH_AMOUNT_QUERIES = {
        "high_amount_cases": Template("""
            SELECT COUNT(*) as total_count 
            FROM anti_fraud_case_new 
            WHERE involved_amount >= $amount 
              AND insert_day = '$date'
        """),
        
        "high_amount_with_operator": Template("""
            SELECT COUNT(*) as total_count 
            FROM anti_fraud_case_new 
            WHERE involved_amount >= $amount 
              AND insert_day = '$date'  
              AND suspect_phone_info IS NOT NULL
        """),
        
        "high_amount_telecom": Template("""
            SELECT COUNT(*) as total_count 
            FROM anti_fraud_case_new 
            WHERE involved_amount >= $amount 
              AND insert_day = '$date'  
              AND suspect_phone_info LIKE '%电信%'
        """),
        
        "case_numbers_high_amount": Template("""
            SELECT case_number 
            FROM anti_fraud_case_new 
            WHERE involved_amount >= $amount 
              AND insert_day = '$date'
        """)
    }
    
    # 图表数据查询
    CHART_QUERIES = {
        "monthly_trend": Template("""
            SELECT insert_day as x_date,
                   COUNT(1) as total_count 
            FROM anti_fraud_case_new 
            WHERE suspect_phone_info LIKE '%$operator%'  
              AND DATE_FORMAT(insert_day, '%Y-%m') = DATE_FORMAT('$date', '%Y-%m') 
            GROUP BY insert_day 
            ORDER BY insert_day ASC
        """),
        
        "weekly_trend": Template("""
            SELECT insert_day as x_date,
                   COUNT(1) as total_count 
            FROM anti_fraud_case_new 
            WHERE suspect_phone_info LIKE '%$operator%'  
              AND STR_TO_DATE(insert_day, '%Y-%m-%d') > DATE_FORMAT(DATE_SUB('$date', INTERVAL 7 DAY), '%Y-%m-%d') 
            GROUP BY insert_day 
            ORDER BY insert_day ASC
        """)
    }
    
    # 月度统计查询
    MONTHLY_STATS = {
        "monthly_total": Template("""
            SELECT COUNT(1) as total_count 
            FROM anti_fraud_case_new 
            WHERE DATE_FORMAT(STR_TO_DATE(insert_day, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT('$date', '%Y-%m')
        """),
        
        "monthly_telecom": Template("""
            SELECT COUNT(1) as total_count 
            FROM anti_fraud_case_new 
            WHERE DATE_FORMAT(STR_TO_DATE(insert_day, '%Y-%m-%d'), '%Y-%m') = DATE_FORMAT('$date', '%Y-%m') 
              AND suspect_phone_info LIKE '%电信%'
        """)
    }


def format_excluded_apps(excluded_apps: tuple) -> str:
    """格式化排除的APP名称列表为SQL IN子句"""
    formatted_apps = [f"'{app}'" for app in excluded_apps]
    return f"({', '.join(formatted_apps)})"


def get_query(template: Template, **kwargs) -> str:
    """
    根据模板和参数生成SQL查询
    
    Args:
        template: SQL模板
        **kwargs: 模板参数
    
    Returns:
        格式化后的SQL查询字符串
    """
    return template.safe_substitute(**kwargs)
