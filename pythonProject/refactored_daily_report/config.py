"""
配置管理模块
统一管理所有配置项，支持从环境变量和配置文件加载
"""
import os
from dataclasses import dataclass
from typing import Dict, Any, Optional
from datetime import datetime, timedelta


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: int = 3306
    username: str = "root"
    password: str = ""
    database: str = "anti_fraud"
    charset: str = "utf8mb4"
    
    @property
    def connection_url(self) -> str:
        """获取数据库连接URL"""
        return f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"


@dataclass
class FileConfig:
    """文件路径配置"""
    base_path: str = "/Users/<USER>/Documents/日报数据"
    template_path: str = "templates"
    output_path: str = "output"
    
    def get_date_path(self, date: str) -> str:
        """获取指定日期的文件路径"""
        year_month = date[:7]  # YYYY-MM
        return os.path.join(self.base_path, year_month)
    
    def get_output_filename(self, date: str, report_type: str = "日报") -> str:
        """获取输出文件名"""
        return f"{date}案情分析{report_type}.docx"


@dataclass
class ReportConfig:
    """报告配置"""
    date_query: str = ""
    date_range: str = ""
    intensive_monitoring: str = "96106"
    
    def __post_init__(self):
        if not self.date_query:
            # 默认使用昨天的日期
            yesterday = datetime.now() - timedelta(days=1)
            self.date_query = yesterday.strftime("%Y-%m-%d")
        
        if not self.date_range:
            # 根据日期生成中文日期范围
            date_obj = datetime.strptime(self.date_query, "%Y-%m-%d")
            self.date_range = f"{date_obj.year}年{date_obj.month}月{date_obj.day:02d}日"


@dataclass
class QueryConfig:
    """查询配置"""
    # 运营商相关配置
    operators: Dict[str, str] = None
    
    # 金额阈值配置
    amount_thresholds: Dict[str, int] = None
    
    # 排除的APP名称
    excluded_app_names: tuple = None
    
    def __post_init__(self):
        if self.operators is None:
            self.operators = {
                "电信": "电信",
                "联通": "联通", 
                "移动": "移动",
                "广电": "广电",
                "虚商": "虚商"
            }
        
        if self.amount_thresholds is None:
            self.amount_thresholds = {
                "high_50": 500000,      # 50万
                "high_100": 1000000,    # 100万
                "high_1000": 10000000   # 1000万
            }
        
        if self.excluded_app_names is None:
            self.excluded_app_names = ('', '-', '无', '韩文', '符号', '未命名', '日文', '不详', '？？')


class Config:
    """主配置类"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.db = DatabaseConfig()
        self.file = FileConfig()
        self.report = ReportConfig()
        self.query = QueryConfig()
        
        # 如果提供了配置文件，则加载配置
        if config_file and os.path.exists(config_file):
            self._load_from_file(config_file)
        
        # 从环境变量加载配置
        self._load_from_env()
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # 数据库配置
        if os.getenv("DB_HOST"):
            self.db.host = os.getenv("DB_HOST")
        if os.getenv("DB_PORT"):
            self.db.port = int(os.getenv("DB_PORT"))
        if os.getenv("DB_USERNAME"):
            self.db.username = os.getenv("DB_USERNAME")
        if os.getenv("DB_PASSWORD"):
            self.db.password = os.getenv("DB_PASSWORD")
        if os.getenv("DB_DATABASE"):
            self.db.database = os.getenv("DB_DATABASE")
        
        # 文件路径配置
        if os.getenv("FILE_BASE_PATH"):
            self.file.base_path = os.getenv("FILE_BASE_PATH")
        
        # 报告配置
        if os.getenv("REPORT_DATE"):
            self.report.date_query = os.getenv("REPORT_DATE")
            # 重新计算日期范围
            date_obj = datetime.strptime(self.report.date_query, "%Y-%m-%d")
            self.report.date_range = f"{date_obj.year}年{date_obj.month}月{date_obj.day:02d}日"
    
    def _load_from_file(self, config_file: str):
        """从配置文件加载配置"""
        # TODO: 实现从JSON/YAML文件加载配置
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            "database": {
                "host": self.db.host,
                "port": self.db.port,
                "username": self.db.username,
                "database": self.db.database
            },
            "file": {
                "base_path": self.file.base_path,
                "template_path": self.file.template_path,
                "output_path": self.file.output_path
            },
            "report": {
                "date_query": self.report.date_query,
                "date_range": self.report.date_range,
                "intensive_monitoring": self.report.intensive_monitoring
            },
            "query": {
                "operators": self.query.operators,
                "amount_thresholds": self.query.amount_thresholds,
                "excluded_app_names": list(self.query.excluded_app_names)
            }
        }


# 全局配置实例
config = Config()
