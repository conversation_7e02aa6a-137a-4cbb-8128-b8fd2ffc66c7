"""
数据处理模块
负责将原始查询结果转换为报告所需的数据结构
"""
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import re

from .models import (
    ReportData, CaseStatistics, OperatorStatistics, AppStatistics,
    CaseTypeStatistics, PhoneLocationInfo, TrendData, ChartData,
    MonitoringData, QueryResult
)
from .config import config

logger = logging.getLogger(__name__)


class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.config = config
    
    def process_basic_statistics(self, basic_data: Dict[str, QueryResult]) -> CaseStatistics:
        """
        处理基础统计数据
        
        Args:
            basic_data: 基础统计查询结果
            
        Returns:
            CaseStatistics: 处理后的案件统计数据
        """
        logger.info("开始处理基础统计数据")
        
        stats = CaseStatistics()
        
        try:
            # 总案件数和金额
            if basic_data["total_cases"].success and basic_data["total_cases"].data:
                total_data = basic_data["total_cases"].data[0]
                stats.total_count = int(total_data.get("case_count", 0))
                stats.total_amount = float(total_data.get("total_amount", 0.0))
            
            # URL案件
            if basic_data["url_cases"].success and basic_data["url_cases"].data:
                url_data = basic_data["url_cases"].data[0]
                stats.url_count = int(url_data.get("total_count", 0))
                stats.url_percentage = float(url_data.get("percentage", 0.0))
            
            # APP案件
            if basic_data["app_cases"].success and basic_data["app_cases"].data:
                app_data = basic_data["app_cases"].data[0]
                stats.app_count = int(app_data.get("total_count", 0))
                stats.app_percentage = float(app_data.get("percentage", 0.0))
            
            # 短信案件
            if basic_data["sms_cases"].success and basic_data["sms_cases"].data:
                sms_data = basic_data["sms_cases"].data[0]
                stats.sms_count = int(sms_data.get("total_count", 0))
                stats.sms_percentage = float(sms_data.get("percentage", 0.0))
            
            # 国际号码案件
            if basic_data["international_cases"].success and basic_data["international_cases"].data:
                intl_data = basic_data["international_cases"].data[0]
                stats.international_count = int(intl_data.get("tel_count", 0))
            
            logger.info("基础统计数据处理完成")
            
        except Exception as e:
            logger.error(f"处理基础统计数据时发生错误: {e}")
        
        return stats
    
    def process_case_types(self, case_type_result: QueryResult) -> List[CaseTypeStatistics]:
        """
        处理案件类型统计数据
        
        Args:
            case_type_result: 案件类型查询结果
            
        Returns:
            List[CaseTypeStatistics]: 案件类型统计列表
        """
        logger.info("开始处理案件类型统计数据")
        
        case_types = []
        
        try:
            if case_type_result.success and case_type_result.data:
                for item in case_type_result.data:
                    case_type = CaseTypeStatistics(
                        case_type=str(item.get("type", "")),
                        total_count=int(item.get("total_count", 0)),
                        amount=float(item.get("amount", 0.0)),
                        percentage=float(item.get("percentage", 0.0))
                    )
                    case_types.append(case_type)
            
            logger.info(f"案件类型统计数据处理完成，共 {len(case_types)} 种类型")
            
        except Exception as e:
            logger.error(f"处理案件类型统计数据时发生错误: {e}")
        
        return case_types
    
    def process_operator_statistics(self, operator_data: Dict[str, Dict[str, QueryResult]]) -> Dict[str, OperatorStatistics]:
        """
        处理运营商统计数据
        
        Args:
            operator_data: 运营商查询结果
            
        Returns:
            Dict[str, OperatorStatistics]: 运营商统计数据
        """
        logger.info("开始处理运营商统计数据")
        
        operators = {}
        
        try:
            for operator_name, data in operator_data.items():
                stats = OperatorStatistics(name=operator_name)
                
                # 基础统计
                if data["basic"].success and data["basic"].data:
                    basic = data["basic"].data[0]
                    stats.total_count = int(basic.get("total_count", 0))
                    stats.total_amount = float(basic.get("total_money", 0.0))
                
                # 高金额案件统计
                if data["high_50"].success and data["high_50"].data:
                    stats.high_amount_50 = int(data["high_50"].data[0].get("total_count", 0))
                
                if data["high_100"].success and data["high_100"].data:
                    stats.high_amount_100 = int(data["high_100"].data[0].get("total_count", 0))
                
                if data["high_1000"].success and data["high_1000"].data:
                    stats.high_amount_1000 = int(data["high_1000"].data[0].get("total_count", 0))
                
                operators[operator_name] = stats
            
            # 计算占比
            total_count = sum(op.total_count for op in operators.values())
            for operator in operators.values():
                operator.calculate_percentage(total_count)
            
            logger.info(f"运营商统计数据处理完成，共 {len(operators)} 个运营商")
            
        except Exception as e:
            logger.error(f"处理运营商统计数据时发生错误: {e}")
        
        return operators
    
    def process_app_statistics(self, app_data: Dict[str, QueryResult], new_apps: List[Dict[str, Any]] = None) -> tuple:
        """
        处理APP统计数据
        
        Args:
            app_data: APP查询结果
            new_apps: 新增APP列表
            
        Returns:
            tuple: (APP统计列表, APP总数, APP总金额, 新增APP数量)
        """
        logger.info("开始处理APP统计数据")
        
        app_stats = []
        app_total_count = 0
        app_total_amount = 0.0
        new_app_count = 0
        
        try:
            # 获取APP总数
            if app_data["app_total"].success and app_data["app_total"].data:
                app_total_count = int(app_data["app_total"].data[0].get("app_total", 0))
            
            # 获取APP总金额
            if app_data["app_amount_stats"].success and app_data["app_amount_stats"].data:
                amount_data = app_data["app_amount_stats"].data[0]
                app_total_amount = float(amount_data.get("amount", 0.0))
            
            # 处理TOP APP列表
            if app_data["top_apps"].success and app_data["top_apps"].data:
                new_app_names = set()
                if new_apps:
                    new_app_names = {app.get("app_name", "") for app in new_apps}
                    new_app_count = len(new_apps)
                
                for item in app_data["top_apps"].data:
                    app_name = str(item.get("app_name", ""))
                    app_stat = AppStatistics(
                        app_name=app_name,
                        total_count=int(item.get("total_count", 0)),
                        percentage=float(item.get("percentage", 0.0)),
                        is_new=app_name in new_app_names
                    )
                    app_stats.append(app_stat)
            
            logger.info(f"APP统计数据处理完成，共 {len(app_stats)} 个APP，新增 {new_app_count} 个")
            
        except Exception as e:
            logger.error(f"处理APP统计数据时发生错误: {e}")
        
        return app_stats, app_total_count, app_total_amount, new_app_count
    
    def process_phone_locations(self, location_data: Dict[str, List[QueryResult]]) -> Dict[str, List[PhoneLocationInfo]]:
        """
        处理手机号归属地数据
        
        Args:
            location_data: 手机号归属地查询结果
            
        Returns:
            Dict[str, List[PhoneLocationInfo]]: 手机号归属地信息
        """
        logger.info("开始处理手机号归属地数据")
        
        locations = {}
        
        try:
            for operator, results in location_data.items():
                operator_locations = []
                
                # 50万-100万
                if results[0].success and results[0].data:
                    for item in results[0].data:
                        location = PhoneLocationInfo(
                            phone_info=str(item.get("suspect_phone_info", "")),
                            amount_range="50-100万"
                        )
                        operator_locations.append(location)
                
                # 100万-1000万
                if results[1].success and results[1].data:
                    for item in results[1].data:
                        location = PhoneLocationInfo(
                            phone_info=str(item.get("suspect_phone_info", "")),
                            amount_range="100-1000万"
                        )
                        operator_locations.append(location)
                
                # 1000万以上
                if results[2].success and results[2].data:
                    for item in results[2].data:
                        location = PhoneLocationInfo(
                            phone_info=str(item.get("suspect_phone_info", "")),
                            amount_range="1000万以上"
                        )
                        operator_locations.append(location)
                
                locations[operator] = operator_locations
            
            logger.info(f"手机号归属地数据处理完成，共 {len(locations)} 个运营商")
            
        except Exception as e:
            logger.error(f"处理手机号归属地数据时发生错误: {e}")
        
        return locations
    
    def process_trend_data(self, trend_data: Dict[str, QueryResult]) -> Dict[str, TrendData]:
        """
        处理趋势数据
        
        Args:
            trend_data: 趋势查询结果
            
        Returns:
            Dict[str, TrendData]: 趋势数据
        """
        logger.info("开始处理趋势数据")
        
        trends = {}
        
        try:
            # 日环比
            daily_trend = TrendData()
            if trend_data["daily_comparison"].success and trend_data["daily_comparison"].data:
                daily_trend.current_count = int(trend_data["daily_comparison"].data[0].get("case_count", 0))
            
            if trend_data["previous_day"].success and trend_data["previous_day"].data:
                daily_trend.previous_count = int(trend_data["previous_day"].data[0].get("case_count", 0))
            
            daily_trend.calculate_trend()
            trends["daily"] = daily_trend
            
            # 月环比
            monthly_trend = TrendData()
            if trend_data["monthly_comparison"].success and trend_data["monthly_comparison"].data:
                monthly_trend.current_count = int(trend_data["monthly_comparison"].data[0].get("case_count", 0))
            
            if trend_data["previous_month"].success and trend_data["previous_month"].data:
                monthly_trend.previous_count = int(trend_data["previous_month"].data[0].get("case_count", 0))
            
            monthly_trend.calculate_trend()
            trends["monthly"] = monthly_trend
            
            # 电信环比
            telecom_trend = TrendData()
            if trend_data["daily_comparison"].success and trend_data["daily_comparison"].data:
                # 这里需要从运营商数据中获取电信当日数据，暂时使用0
                telecom_trend.current_count = 0
            
            if trend_data["telecom_yesterday"].success and trend_data["telecom_yesterday"].data:
                telecom_trend.previous_count = int(trend_data["telecom_yesterday"].data[0].get("total_count", 0))
            
            telecom_trend.calculate_trend()
            trends["telecom"] = telecom_trend
            
            logger.info("趋势数据处理完成")
            
        except Exception as e:
            logger.error(f"处理趋势数据时发生错误: {e}")
        
        return trends
    
    def process_chart_data(self, chart_data: Dict[str, QueryResult]) -> Dict[str, ChartData]:
        """
        处理图表数据
        
        Args:
            chart_data: 图表查询结果
            
        Returns:
            Dict[str, ChartData]: 图表数据
        """
        logger.info("开始处理图表数据")
        
        charts = {}
        
        try:
            for operator, result in chart_data.items():
                chart = ChartData(label=operator)
                
                if result.success and result.data:
                    chart.x_data = [item.get("x_date", "") for item in result.data]
                    chart.y_data = [int(item.get("total_count", 0)) for item in result.data]
                
                charts[operator] = chart
            
            logger.info(f"图表数据处理完成，共 {len(charts)} 个运营商")
            
        except Exception as e:
            logger.error(f"处理图表数据时发生错误: {e}")
        
        return charts
    
    def process_monitoring_data(self, monitoring_result: QueryResult) -> MonitoringData:
        """
        处理重点监测数据
        
        Args:
            monitoring_result: 重点监测查询结果
            
        Returns:
            MonitoringData: 重点监测数据
        """
        logger.info("开始处理重点监测数据")
        
        monitoring = MonitoringData(
            monitoring_numbers=config.report.intensive_monitoring
        )
        
        try:
            if monitoring_result.success and monitoring_result.data:
                monitoring.case_count = int(monitoring_result.data[0].get("total_count", 0))
            
            logger.info("重点监测数据处理完成")
            
        except Exception as e:
            logger.error(f"处理重点监测数据时发生错误: {e}")
        
        return monitoring
    
    def process_high_amount_data(self, high_amount_data: Dict[str, QueryResult]) -> tuple:
        """
        处理高金额案件数据
        
        Args:
            high_amount_data: 高金额案件查询结果
            
        Returns:
            tuple: (高金额案件编号列表, 50万以上案件数, 50万以上运营商案件数, 50万以上电信案件数)
        """
        logger.info("开始处理高金额案件数据")
        
        case_numbers = []
        high_50_count = 0
        high_50_operator_count = 0
        high_50_telecom_count = 0
        
        try:
            # 50万以上案件总数
            if high_amount_data["total"].success and high_amount_data["total"].data:
                high_50_count = int(high_amount_data["total"].data[0].get("total_count", 0))
            
            # 50万以上运营商案件数
            if high_amount_data["with_operator"].success and high_amount_data["with_operator"].data:
                high_50_operator_count = int(high_amount_data["with_operator"].data[0].get("total_count", 0))
            
            # 50万以上电信案件数
            if high_amount_data["telecom"].success and high_amount_data["telecom"].data:
                high_50_telecom_count = int(high_amount_data["telecom"].data[0].get("total_count", 0))
            
            # 高金额案件编号
            if high_amount_data["case_numbers"].success and high_amount_data["case_numbers"].data:
                case_numbers = [str(item.get("case_number", "")) for item in high_amount_data["case_numbers"].data]
            
            logger.info(f"高金额案件数据处理完成，共 {len(case_numbers)} 个高金额案件")
            
        except Exception as e:
            logger.error(f"处理高金额案件数据时发生错误: {e}")
        
        return case_numbers, high_50_count, high_50_operator_count, high_50_telecom_count
    
    def process_monthly_statistics(self, monthly_data: Dict[str, QueryResult]) -> tuple:
        """
        处理月度统计数据
        
        Args:
            monthly_data: 月度统计查询结果
            
        Returns:
            tuple: (月度总案件数, 月度电信案件数, 月度电信占比)
        """
        logger.info("开始处理月度统计数据")
        
        monthly_total = 0
        monthly_telecom = 0
        monthly_telecom_percentage = 0.0
        
        try:
            # 月度总案件数
            if monthly_data["monthly_total"].success and monthly_data["monthly_total"].data:
                monthly_total = int(monthly_data["monthly_total"].data[0].get("total_count", 0))
            
            # 月度电信案件数
            if monthly_data["monthly_telecom"].success and monthly_data["monthly_telecom"].data:
                monthly_telecom = int(monthly_data["monthly_telecom"].data[0].get("total_count", 0))
            
            # 计算占比
            if monthly_total > 0:
                monthly_telecom_percentage = round((monthly_telecom / monthly_total) * 100, 2)
            
            logger.info("月度统计数据处理完成")
            
        except Exception as e:
            logger.error(f"处理月度统计数据时发生错误: {e}")
        
        return monthly_total, monthly_telecom, monthly_telecom_percentage
    
    def build_report_data(self, all_data: Dict[str, Any]) -> ReportData:
        """
        构建完整的报告数据
        
        Args:
            all_data: 所有处理后的数据
            
        Returns:
            ReportData: 完整的报告数据
        """
        logger.info("开始构建报告数据")
        
        report = ReportData(
            date_range=config.report.date_range,
            date_query=config.report.date_query
        )
        
        try:
            # 基础统计
            report.case_stats = all_data.get("basic_stats", CaseStatistics())
            
            # 运营商统计
            report.operators = all_data.get("operators", {})
            
            # APP统计
            report.app_stats = all_data.get("app_stats", [])
            report.app_total_count = all_data.get("app_total_count", 0)
            report.app_total_amount = all_data.get("app_total_amount", 0.0)
            report.new_app_count = all_data.get("new_app_count", 0)
            
            # 案件类型统计
            report.case_types = all_data.get("case_types", [])
            
            # 手机号归属地信息
            report.phone_locations = all_data.get("phone_locations", {})
            
            # 趋势数据
            trends = all_data.get("trends", {})
            report.daily_trend = trends.get("daily", TrendData())
            report.monthly_trend = trends.get("monthly", TrendData())
            report.telecom_trend = trends.get("telecom", TrendData())
            
            # 图表数据
            report.chart_data = all_data.get("charts", {})
            
            # 重点监测
            report.monitoring = all_data.get("monitoring", MonitoringData())
            
            # 高金额案件
            report.high_amount_cases = all_data.get("case_numbers", [])
            report.high_amount_50_count = all_data.get("high_50_count", 0)
            report.high_amount_50_operator_count = all_data.get("high_50_operator_count", 0)
            report.high_amount_50_telecom_count = all_data.get("high_50_telecom_count", 0)
            
            # 月度累计数据
            report.monthly_total_count = all_data.get("monthly_total", 0)
            report.monthly_telecom_count = all_data.get("monthly_telecom", 0)
            report.monthly_telecom_percentage = all_data.get("monthly_telecom_percentage", 0.0)
            
            logger.info("报告数据构建完成")
            
        except Exception as e:
            logger.error(f"构建报告数据时发生错误: {e}")
        
        return report
