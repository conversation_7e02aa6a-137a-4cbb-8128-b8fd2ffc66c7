    def get_daily_data(self, days: int = 30) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """获取日维度数据"""
        if not self.is_connected and not self.connect():
            return None, None
        
        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            print("🔍 查询数据：" + start_date + " 到 " + end_date + " (" + str(days) + "天)")
            
            # 主查询 - 使用字符串拼接
            query = (
                "SELECT "
                "DATE(insert_day) as date_col, "
                "SUM(CASE WHEN suspect_phone_info LIKE '%电信%' THEN 1 ELSE 0 END) as telecom, "
                "SUM(CASE WHEN suspect_phone_info LIKE '%联通%' THEN 1 ELSE 0 END) as unicom, "
                "SUM(CASE WHEN suspect_phone_info LIKE '%移动%' THEN 1 ELSE 0 END) as mobile "
                "FROM anti_fraud_case_new "
                "WHERE DATE(insert_day) BETWEEN '" + start_date + "' AND '" + end_date + "' "
                "AND involved_amount >= 500000 "
                "GROUP BY DATE(insert_day) "
                "ORDER BY DATE(insert_day)"
            )
            
            df = pd.read_sql(query, self.engine)
            
            # 案情详情查询
            detail_query = (
                "SELECT "
                "DATE(insert_day) as case_date, "
                "brief_case_description as case_desc, "
                "involved_amount, "
                "suspect_phone_info "
                "FROM anti_fraud_case_new "
                "WHERE DATE(insert_day) BETWEEN '" + start_date + "' AND '" + end_date + "' "
                "AND involved_amount >= 500000 "
                "AND (suspect_phone_info LIKE '%电信%' OR suspect_phone_info LIKE '%联通%' OR suspect_phone_info LIKE '%移动%') "
                "ORDER BY insert_day DESC "
                "LIMIT 50"
            )
            
            case_details = pd.read_sql(detail_query, self.engine)
            
            if not df.empty:
                # 重命名列为中文
                df = df.rename(columns={
                    'date_col': '日期',
                    'telecom': '电信',
                    'unicom': '联通', 
                    'mobile': '移动'
                })
                df['日期'] = pd.to_datetime(df['日期'])
                
                # 填充缺失日期
                df = self._fill_missing_dates(df, start_date, end_date)
                
                print("✅ 查询成功，共 " + str(len(df)) + " 条记录")
                return df, case_details
            else:
                print("⚠️ 查询结果为空")
                return self._create_empty_df(start_date, end_date), case_details
                
        except Exception as e:
            print("❌ 日维度数据查询失败: " + str(e))
            return None, None