#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试APP名称过滤功能
"""

import pandas as pd
from datetime import datetime, timedelta

def filter_invalid_app_names(df):
    """过滤无意义的APP名称"""
    if df is None or df.empty:
        return df

    # 定义无意义的APP名称模式
    invalid_patterns = [
        '无', '-', '—', '/', '\\', '未知', 'unknown', 'null', 'NULL',
        '1', '2', '3', '4', '5', '6', '7', '8', '9', '0',
        '其他', '其它', '不详', '不明', '暂无', '待定',
        '***', '###', '...', '。。。', '???',
        'test', 'Test', 'TEST', 'demo', 'Demo', 'DEMO'
    ]

    # 显示过滤前的APP数量
    original_apps = df['app_name'].nunique()
    print(f"🔍 过滤前APP数量: {original_apps}")
    print(f"📋 过滤前APP列表: {sorted(df['app_name'].unique())}")
    
    # 过滤条件
    mask = pd.Series(True, index=df.index)

    # 1. 过滤空值和空字符串
    mask &= df['app_name'].notna()
    mask &= df['app_name'].str.strip() != ''

    # 2. 过滤无意义的模式
    for pattern in invalid_patterns:
        mask &= df['app_name'].str.strip() != pattern

    # 3. 过滤纯数字
    mask &= ~df['app_name'].str.strip().str.isdigit()

    # 4. 过滤纯特殊符号（长度小于等于3且包含特殊符号）
    special_chars_pattern = r'^[^\w\u4e00-\u9fff]{1,3}$'
    mask &= ~df['app_name'].str.match(special_chars_pattern, na=False)

    # 5. 过滤长度过短的名称（小于2个字符，除非是知名APP）
    known_short_apps = ['QQ', 'UC', '58', '12306', '360', 'WPS']
    short_mask = (df['app_name'].str.len() >= 2) | df['app_name'].isin(known_short_apps)
    mask &= short_mask

    filtered_df = df[mask].copy()
    
    # 显示过滤结果
    filtered_apps = filtered_df['app_name'].nunique()
    print(f"✅ 过滤后APP数量: {filtered_apps}")
    print(f"📋 过滤后APP列表: {sorted(filtered_df['app_name'].unique())}")

    if len(filtered_df) < len(df):
        removed_count = len(df) - len(filtered_df)
        print(f"🧹 已过滤 {removed_count} 条无效APP记录")
        
        # 显示被过滤的APP示例
        removed_apps = set(df['app_name'].unique()) - set(filtered_df['app_name'].unique())
        if removed_apps:
            print(f"🗑️ 被过滤的APP: {sorted(list(removed_apps))}")

    return filtered_df

def create_test_data():
    """创建测试数据"""
    # 创建包含有效和无效APP名称的测试数据
    test_data = []
    
    # 有效的APP名称
    valid_apps = ['微信', '支付宝', '抖音', '淘宝', 'QQ', 'UC浏览器']
    
    # 无效的APP名称
    invalid_apps = ['无', '-', '1', '2', '***', '未知', 'null', '其他', '...', '???']
    
    all_apps = valid_apps + invalid_apps
    
    # 生成测试数据
    for i, app in enumerate(all_apps):
        test_data.append({
            'date_col': datetime.now() - timedelta(days=1),
            'app_name': app,
            'case_count': i + 1
        })
    
    return pd.DataFrame(test_data)

def main():
    """主函数"""
    print("🧪 APP名称过滤功能测试")
    print("=" * 50)
    
    # 创建测试数据
    df = create_test_data()
    print(f"📊 测试数据创建完成，共 {len(df)} 条记录")
    
    # 执行过滤
    print("\n🔍 开始过滤...")
    filtered_df = filter_invalid_app_names(df)
    
    print(f"\n📈 过滤结果:")
    print(f"  - 原始记录数: {len(df)}")
    print(f"  - 过滤后记录数: {len(filtered_df)}")
    print(f"  - 过滤效率: {(len(df) - len(filtered_df)) / len(df) * 100:.1f}%")

if __name__ == "__main__":
    main()
