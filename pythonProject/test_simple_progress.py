#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化版进度条 - 绝不滚屏
"""

import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.idea/Ready/test/50万趋势'))

def test_simple_progress():
    """测试简化版进度条"""
    print("🧪 测试简化版进度条 - 绝不滚屏")
    print("=" * 60)
    
    try:
        from ai_service import AIAnalysisProgressBar
        
        # 创建进度条实例
        progress_bar = AIAnalysisProgressBar()
        progress_bar.start(731)  # 模拟提示词长度
        
        # 模拟完整的分析流程
        total_time = 15  # 总演示时间15秒
        update_interval = 0.3
        
        for i in range(int(total_time / update_interval)):
            elapsed = i * update_interval
            
            # 根据时间自动推进阶段
            if elapsed >= 3 and progress_bar.current_stage == 0:
                progress_bar.next_stage()
            elif elapsed >= 5 and progress_bar.current_stage == 1:
                progress_bar.next_stage()
            elif elapsed >= 8 and progress_bar.current_stage == 2:
                progress_bar.next_stage()
                
            progress_bar.display_progress()
            time.sleep(update_interval)
        
        # 完成进度条
        progress_bar.complete()
        
        print("\n✅ 简化版进度条测试完成！")
        print("\n现在的效果:")
        print("--------------------------------------------------")
        print("🤖 正在执行综合分析...")
        print("✅ AI分析完成        |████████████████████| 100% 用时> 15.0s")
        print("")
        print("✅ AI分析完成，总耗时: 15.0秒")
        print("--------------------------------------------------")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_progress()
