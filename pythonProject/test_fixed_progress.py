#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的进度条
"""

import sys
import os
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.idea/Ready/test/50万趋势'))

def test_fixed_progress_bar():
    """测试修复后的进度条"""
    print("🧪 测试修复后的AI智能分析进度条")
    print("=" * 60)
    
    try:
        from ai_service import AIAnalysisProgressBar
        
        # 创建进度条实例
        progress_bar = AIAnalysisProgressBar()
        progress_bar.start(731)  # 模拟提示词长度
        
        # 模拟完整的分析流程
        total_time = 20  # 总演示时间20秒
        update_interval = 0.5
        
        for i in range(int(total_time / update_interval)):
            elapsed = i * update_interval
            
            # 根据时间自动推进阶段
            if elapsed >= 3 and progress_bar.current_stage == 0:
                progress_bar.next_stage()
                print()  # 换行，进入下一阶段
            elif elapsed >= 5 and progress_bar.current_stage == 1:
                progress_bar.next_stage()
                print()  # 换行，进入下一阶段
            elif elapsed >= 10 and progress_bar.current_stage == 2:
                progress_bar.next_stage()
                print()  # 换行，进入下一阶段
                
            progress_bar.display_progress()
            time.sleep(update_interval)
        
        # 完成进度条
        progress_bar.complete()
        
        print("\n✅ 进度条测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_ai_service_integration():
    """测试AI服务集成"""
    print("\n🧪 测试AI服务集成")
    print("=" * 60)
    
    try:
        from ai_service import AIService
        
        # 创建AI服务实例
        ai_service = AIService()
        print("✅ AI服务创建成功")
        
        # 测试get_available_analyses方法
        analyses = ai_service.get_available_analyses()
        print(f"✅ 获取到 {len(analyses)} 个可用分析:")
        for key, name in analyses.items():
            print(f"  - {key}: {name}")
            
        print("\n✅ AI服务集成测试完成！")
        
    except Exception as e:
        print(f"❌ AI服务测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 修复后的进度条测试")
    print("=" * 60)
    
    # 测试进度条
    test_fixed_progress_bar()
    
    # 测试AI服务
    test_ai_service_integration()
    
    print("\n🎉 所有测试完成！")
