#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统计功能修复
验证统计数据概览的显示是否正确
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_data():
    """创建测试数据"""
    print("🔍 创建测试数据...")
    
    # 创建30天的测试数据（截止到昨天）
    yesterday = datetime.now() - timedelta(days=1)
    start_date = yesterday - timedelta(days=29)
    
    date_range = pd.date_range(start=start_date, end=yesterday, freq='D')
    
    # 模拟案件数据
    np.random.seed(42)  # 确保结果可重现
    
    data = {
        '日期': date_range,
        '电信': np.random.poisson(2, len(date_range)),  # 电信平均每天2起
        '联通': np.random.poisson(1.5, len(date_range)),  # 联通平均每天1.5起
        '移动': np.random.poisson(3, len(date_range))   # 移动平均每天3起
    }
    
    df = pd.DataFrame(data)
    
    print(f"✅ 测试数据创建完成")
    print(f"   时间范围: {start_date.strftime('%Y-%m-%d')} 至 {yesterday.strftime('%Y-%m-%d')}")
    print(f"   数据行数: {len(df)}")
    print(f"   电信总案件: {df['电信'].sum()}")
    print(f"   联通总案件: {df['联通'].sum()}")
    print(f"   移动总案件: {df['移动'].sum()}")
    print(f"   总案件数: {df[['电信', '联通', '移动']].sum().sum()}")
    
    return df

def test_statistics_service():
    """测试统计服务"""
    print("\n🔍 测试统计服务...")
    
    try:
        from statistics_service import StatisticsService
        
        # 创建统计服务实例
        stats_service = StatisticsService()
        print("✅ 统计服务导入成功")
        
        # 创建测试数据
        df = create_test_data()
        
        # 计算统计数据
        print("\n📊 计算综合统计数据...")
        stats = stats_service.calculate_comprehensive_stats(df)
        
        if stats:
            print("✅ 统计计算成功")
            
            # 显示关键统计项
            print(f"\n📋 关键统计项:")
            print(f"   统计周期: {stats.get('start_date')} 至 {stats.get('end_date')}")
            print(f"   总案件数: {stats.get('total_cases')}")
            print(f"   日均案件: {stats.get('daily_avg'):.1f}")
            print(f"   电信案件: {stats.get('telecom_total')}")
            print(f"   联通案件: {stats.get('unicom_total')}")
            print(f"   移动案件: {stats.get('mobile_total')}")
            
            # 电信专项统计
            print(f"\n📈 电信专项统计:")
            print(f"   近7天案件: {stats.get('telecom_recent_7_total')}")
            print(f"   前7天案件: {stats.get('telecom_prev_7_total')}")
            print(f"   7天变化率: {stats.get('telecom_7days_change', 0):+.1%}")
            print(f"   本周案件: {stats.get('telecom_current_week_total')}")
            print(f"   上周案件: {stats.get('telecom_last_week_total')}")
            print(f"   周变化率: {stats.get('telecom_week_change', 0):+.1%}")
            print(f"   本月案件: {stats.get('telecom_current_month_total')}")
            print(f"   上月同期: {stats.get('telecom_last_month_total')}")
            print(f"   月变化率: {stats.get('telecom_month_change', 0):+.1%}")
            
            return stats
        else:
            print("❌ 统计计算失败")
            return None
            
    except ImportError as e:
        print(f"❌ 统计服务导入失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 统计服务测试失败: {e}")
        return None

def test_ui_service(stats):
    """测试UI服务显示"""
    print("\n🔍 测试UI服务显示...")
    
    if not stats:
        print("❌ 无统计数据可显示")
        return
    
    try:
        from ui_service import UIService
        
        # 创建UI服务实例
        ui_service = UIService()
        print("✅ UI服务导入成功")
        
        # 显示统计数据
        print("\n📊 显示统计数据概览:")
        ui_service.display_statistics(stats)
        
        print("\n✅ UI显示测试完成")
        
    except ImportError as e:
        print(f"❌ UI服务导入失败: {e}")
    except Exception as e:
        print(f"❌ UI服务测试失败: {e}")

def validate_statistics(stats):
    """验证统计数据的正确性"""
    print("\n🔍 验证统计数据正确性...")
    
    if not stats:
        print("❌ 无统计数据可验证")
        return False
    
    errors = []
    
    # 验证基础数据
    total_cases = stats.get('total_cases', 0)
    telecom_total = stats.get('telecom_total', 0)
    unicom_total = stats.get('unicom_total', 0)
    mobile_total = stats.get('mobile_total', 0)
    
    if total_cases != (telecom_total + unicom_total + mobile_total):
        errors.append(f"总案件数不匹配: {total_cases} != {telecom_total + unicom_total + mobile_total}")
    
    # 验证比例
    if total_cases > 0:
        telecom_ratio = stats.get('telecom_ratio', 0)
        expected_ratio = telecom_total / total_cases
        if abs(telecom_ratio - expected_ratio) > 0.001:
            errors.append(f"电信比例不正确: {telecom_ratio:.3f} != {expected_ratio:.3f}")
    
    # 验证日期
    start_date = stats.get('start_date')
    end_date = stats.get('end_date')
    if not start_date or not end_date:
        errors.append("缺少日期信息")
    
    if errors:
        print("❌ 发现验证错误:")
        for error in errors:
            print(f"   - {error}")
        return False
    else:
        print("✅ 统计数据验证通过")
        return True

if __name__ == "__main__":
    print("🚀 开始测试统计功能修复...")
    print("=" * 60)
    
    # 测试统计服务
    stats = test_statistics_service()
    
    # 验证统计数据
    validate_statistics(stats)
    
    # 测试UI显示
    test_ui_service(stats)
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！")
    
    print("\n📋 修复总结:")
    print("1. ✅ 总案件数：包含电信、联通、移动所有案件")
    print("2. ✅ 运营商分布：显示三大运营商分别的案件数和比例")
    print("3. ✅ 电信近7天统计：案件数、日均、变化率")
    print("4. ✅ 电信自然周统计：本周、上周案件数和变化率")
    print("5. ✅ 电信自然月统计：本月、上月同期案件数和变化率")
    print("6. ✅ 时间范围：基于自然周和自然月计算")
