#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
短信诈骗风险识别系统 - 完整交互式版本
Author: Complete Interactive Version
Date: 2025-07-02
Version: 1.0
新增: 完整交互式菜单界面，支持多种操作模式
"""

import re
import logging
import pandas as pd
import time
import hashlib
import gc
import sys
import os
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
from functools import lru_cache
import argparse

try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False

# ==================== 全局工具函数 ====================
def get_content_hash(content: str, sender: str) -> str:
    """生成短信内容的哈希值用于缓存键"""
    hash_obj = hashlib.md5()
    hash_obj.update(content.encode('utf-8'))
    hash_obj.update(sender.encode('utf-8'))
    return hash_obj.hexdigest()

def setup_logger(name: str = __name__, level: int = logging.INFO, log_file: str = None) -> logging.Logger:
    """设置日志处理器"""
    logger = logging.getLogger(name)
    if logger.handlers:
        return logger

    logger.setLevel(level)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件处理器（如果提供了log_file参数）
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger

# ==================== 配置类 ====================
class PathConfig:
    """增强版路径配置类，支持灵活配置和验证"""

    def __init__(self, input_dir: str = None, input_filename: str = None,
                 output_dir: str = None, output_filename: str = None):
        # 默认路径配置
        self.default_input_dir = Path("/Users/<USER>/Documents/测试")
        self.default_output_dir = Path("/Users/<USER>/Documents/测试")
        self.temp_dir = Path("./temp")
        self.cache_dir = Path("./cache")
        self.log_dir = Path("./logs")

        # 用户指定路径
        self.input_dir = Path(input_dir) if input_dir else self.default_input_dir
        self.input_filename = input_filename or "20250303-20250331全国涉案警情106中包含短信内容_副本.xlsx"
        self.output_dir = Path(output_dir) if output_dir else self.default_output_dir
        self.output_filename = output_filename or "诈骗短信结果"

        self.batch_size = 100
        self.output_format = 'excel'
        self.include_summary = True

        # 创建必要目录
        self._create_directories()

    def _create_directories(self):
        """创建必要的目录"""
        for directory in [self.default_input_dir, self.default_output_dir,
                          self.temp_dir, self.cache_dir, self.log_dir]:
            directory.mkdir(parents=True, exist_ok=True)

    def get_input_path(self) -> Path:
        """获取输入文件路径并进行验证"""
        if not self.input_filename.endswith('.xlsx'):
            raise ValueError("输入文件必须是.xlsx格式")
        path = self.input_dir / self.input_filename
        if not path.exists():
            raise FileNotFoundError(f"输入文件不存在: {path}")
        return path

    def get_output_path(self) -> Path:
        """获取输出文件路径并确保目录存在"""
        self.output_dir.mkdir(parents=True, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{self.output_filename}_{timestamp}.xlsx"
        return self.output_dir / filename

    def get_temp_path(self, filename: str) -> Path:
        """获取临时文件路径"""
        return self.temp_dir / filename

    def get_cache_path(self) -> Path:
        """获取缓存文件路径"""
        return self.cache_dir / "analysis_cache.json"

    def get_log_path(self) -> Path:
        """获取日志文件路径"""
        timestamp = datetime.now().strftime('%Y%m%d')
        return self.log_dir / f"sms_fraud_{timestamp}.log"

    def validate(self) -> bool:
        """验证路径配置是否有效"""
        try:
            # 检查输入文件是否存在
            input_path = self.get_input_path()

            # 检查输出目录写入权限
            self.output_dir.mkdir(parents=True, exist_ok=True)
            test_file = self.output_dir / ".permission_test"
            test_file.touch()
            test_file.unlink()
            return True
        except Exception as e:
            logging.getLogger(__name__).error(f"路径配置验证失败: {e}")
            return False

# ==================== 数据结构 ====================
@dataclass
class ExtractedFeatures:
    """提取的特征数据"""
    signature: str
    amounts: List[str]
    phones: List[str]
    urls: List[str]
    verification_codes: List[str]
    fraud_keywords: Dict[str, List[str]]

@dataclass
class SMSAnalysisResult:
    """增强版短信分析结果"""
    案件编号: str
    短信内容: str
    主叫号码: str
    发送时间: str
    短信签名: str
    签名风险: str
    判断原因: str
    敏感词: str
    提取金额: str
    提取电话: str
    提取URL: str
    验证码: str
    风险分数: float
    风险等级: str
    分析耗时: Optional[float] = None

# ==================== 配置管理 ====================
class Config:
    """终极配置管理类，结合静态配置和动态加载能力"""

    # 静态配置
    FRAUD_KEYWORDS = {
        "银行类风险": [
            "冻结", "收费", "扣费", "自动扣款", "自动扣费", "自动支付", "自动代扣",
            "自动划扣", "自动扣除", "自动续费", "自动缴费", "自动交费", "提现", "支付",
            "到账", "可提", "扣-费", "扣款", "扣除", "收取", "划扣", "代扣", "补缴", "余额", "缴款"
        ],
        "续费类风险": [
            "会员到期", "订阅到期", "订阅失败", "会员期满", "余额不足", "到期",
            "积分过期", "过期", "自动续约", "合同", "合约", "开通", "包月",
            "续订", "续费", "续约", "订阅", "会员服务", "VIP"
        ],
        "信贷类风险": [
            "信用评估", "资质", "额度", "授信额度", "临时额度", "贷款额度", "信用额度",
            "临时借款额", "临额", "金额", "预估额", "借款", "快速放款", "下款",
            "生效", "欠款已逾期", "逾期", "欠款", "免息券", "放贷", "低息", "利率",
            "分期", "还款", "贷", "借", "利息", "违约"
        ],
        "威胁恐吓风险": [
            "个人征信", "个人信用", "冻结账户", "造成影响", "集体起诉", "逾期失信",
            "法律责任", "公安机关", "法院", "传票", "立案", "处罚", "黑名单",
            "后果自负", "严重后果", "涉嫌违法", "诉前调解"
        ]
    }

    RISK_WEIGHTS = {
        "keywords": {
            "银行类风险": 4.0, "信贷类风险": 3.5,
            "续费类风险": 3.5, "威胁恐吓风险": 2.5
        },
        "amounts": [(50000, 3.5), (10000, 2.5), (1000, 1.5), (0, 0.8)],
        "phones": {
            "伪装普通号码": 8.0, "国际号码": 8.0, "0开头号码": 5.0,
            "双普通号码": 4.5, "普通号码": 3.5, "包含链接": 3.0
        }
    }

    RISK_THRESHOLDS = {"high": 7.5, "medium": 5.0, "low": 3.0}

    OFFICIAL_SIGNATURE_PATTERNS = [
        r"^【(工商银行|建设银行|农业银行|中国银行|交通银行|招商银行|邮政储蓄银行)】$",
        r"^【(支付宝|微信支付|银联)】$"
    ]

    CACHE_SETTINGS = {
        "max_cache_size": 1000,  # 最大缓存条目数
        "max_cache_age_days": 7,  # 缓存最大保留天数
        "enable_cache": True,
        "cleanup_threshold": 1200,  # 触发清理的阈值
        "cleanup_ratio": 0.2  # 每次清理的比例
    }

    @classmethod
    @lru_cache(maxsize=100)
    def get_compiled_pattern(cls, pattern: str) -> re.Pattern:
        """获取编译后的正则表达式（带缓存）"""
        return re.compile(pattern)

# ==================== 工具模块 ====================
class Logger:
    @staticmethod
    def setup_logger(name: str = __name__, level: int = logging.INFO, log_file: str = None) -> logging.Logger:
        """设置日志处理器"""
        logger = logging.getLogger(name)
        if logger.handlers:
            return logger

        logger.setLevel(level)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        # 文件处理器（如果提供了log_file参数）
        if log_file:
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

        return logger

class CacheManager:
    """增强版缓存管理器，支持自动清理"""

    def __init__(self, cache_file: Path):
        self.cache_file = cache_file
        self.cache_data = {}
        self.load_cache()

        # 自动清理过期缓存
        self.auto_cleanup()

    def auto_cleanup(self):
        """自动清理过期缓存"""
        try:
            if not self.cache_data:
                return

            # 清理策略
            current_time = time.time()
            max_age = Config.CACHE_SETTINGS["max_cache_age_days"] * 86400  # 默认7天

            # 清理过期条目
            initial_count = len(self.cache_data)
            self.cache_data = {
                k: v for k, v in self.cache_data.items()
                if current_time - v.get('last_accessed', 0) <= max_age
            }

            cleaned_count = initial_count - len(self.cache_data)
            if cleaned_count > 0:
                print(f"自动清理缓存: 移除了 {cleaned_count} 条过期记录")

            # 如果缓存仍然过大，按LRU清理
            if len(self.cache_data) > Config.CACHE_SETTINGS["max_cache_size"]:
                self._cleanup_by_lru()

        except Exception as e:
            print(f"自动清理缓存失败: {e}")

    def _cleanup_by_lru(self):
        """按最近最少使用策略清理"""
        sorted_items = sorted(
            self.cache_data.items(),
            key=lambda x: x[1].get('last_accessed', 0)
        )

        # 保留80%的缓存
        keep_count = int(Config.CACHE_SETTINGS["max_cache_size"] * 0.8)
        self.cache_data = dict(sorted_items[-keep_count:])

        print(f"LRU缓存清理完成，保留 {keep_count} 条记录")

    def get(self, key: str) -> Optional[Dict]:
        """获取缓存项并更新访问时间"""
        if key in self.cache_data:
            self.cache_data[key]['last_accessed'] = time.time()
            return self.cache_data[key]['data']
        return None

    def set(self, key: str, value: Dict):
        """设置缓存项"""
        if len(self.cache_data) >= Config.CACHE_SETTINGS["max_cache_size"]:
            self._cleanup_by_lru()

        self.cache_data[key] = {
            'data': value,
            'created_at': time.time(),
            'last_accessed': time.time()
        }

    def load_cache(self):
        """加载缓存"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    self.cache_data = json.load(f)
        except Exception as e:
            print(f"加载缓存失败: {e}")
            self.cache_data = {}

    def save_cache(self):
        """保存缓存"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存缓存失败: {e}")

    def get_cache_size(self) -> int:
        """获取缓存大小"""
        return len(self.cache_data)

    def clear_cache(self):
        """清理缓存"""
        self.cache_data.clear()
        if self.cache_file.exists():
            self.cache_file.unlink()
        print("缓存已清理")

class ProgressBar:
    """增强版进度条，支持多种显示模式"""

    def __init__(self, total: int, prefix: str = "Progress",
                 display_mode: str = "auto"):
        self.total = total
        self.prefix = prefix
        self.current = 0
        self.start_time = time.time()
        self.display_mode = display_mode if display_mode in ("auto", "simple", "bar") else "auto"
        self.last_update = 0

    def update(self, step: int = 1):
        """更新进度"""
        self.current += step
        now = time.time()

        # 控制更新频率，避免频繁刷新
        if now - self.last_update < 0.1 and self.current < self.total:
            return

        self.last_update = now
        self._display()

    def _display(self):
        """根据模式显示进度"""
        if self.display_mode == "bar":
            self._display_bar()
        elif self.display_mode == "simple":
            self._display_simple()
        else:  # auto
            try:
                self._display_bar()
            except:
                self._display_simple()

    def _display_bar(self):
        """显示图形化进度条"""
        if self.total == 0:
            return

        percentage = (self.current / self.total) * 100
        elapsed = time.time() - self.start_time

        # 估算剩余时间
        if self.current > 0:
            eta = (elapsed / self.current) * (self.total - self.current)
            eta_str = f"ETA: {eta:.0f}s"
        else:
            eta_str = "ETA: --"

        # 构建进度条
        bar_length = 30
        filled_length = int(bar_length * self.current // self.total)
        bar = '█' * filled_length + '-' * (bar_length - filled_length)

        print(f'\r{self.prefix}: |{bar}| {self.current}/{self.total} ({percentage:.1f}%) {eta_str}',
              end='', flush=True)

        if self.current >= self.total:
            print()  # 换行

    def _display_simple(self):
        """显示简单进度信息"""
        if self.total == 0:
            return

        percentage = (self.current / self.total) * 100
        elapsed = time.time() - self.start_time

        if self.current > 0:
            eta = (elapsed / self.current) * (self.total - self.current)
            eta_str = f"ETA: {eta:.0f}s"
        else:
            eta_str = "ETA: --"

        print(f'\r{self.prefix}: {self.current}/{self.total} ({percentage:.1f}%) {eta_str}',
              end='', flush=True)

        if self.current >= self.total:
            print()

class PerformanceMonitor:
    """增强的性能监控器"""

    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.memory_samples = []
        self.logger = Logger.setup_logger()

    def start(self):
        """开始监控"""
        self.start_time = time.time()
        self.memory_samples = []
        self.logger.info("性能监控启动...")
        if HAS_PSUTIL:
            self._record_memory()

    def end(self):
        """结束监控"""
        self.end_time = time.time()
        if HAS_PSUTIL:
            self._record_memory()

        if self.start_time:
            duration = self.end_time - self.start_time
            self.logger.info(f"执行完成，总耗时: {duration:.2f}秒")

            if HAS_PSUTIL and self.memory_samples:
                avg_mem = sum(self.memory_samples) / len(self.memory_samples)
                max_mem = max(self.memory_samples)
                self.logger.info(f"平均内存使用: {avg_mem:.2f} MB, 峰值内存: {max_mem:.2f} MB")

            return duration
        return 0

    def _record_memory(self):
        """记录内存使用情况"""
        if HAS_PSUTIL:
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            self.memory_samples.append(memory_mb)

    def get_current_memory(self) -> float:
        """获取当前内存使用"""
        if HAS_PSUTIL:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        return 0

# ==================== 核心功能模块 ====================
class FeatureExtractor:
    """终极特征提取器"""

    def __init__(self):
        self.logger = Logger.setup_logger()
        # 预编译所有正则表达式
        self._patterns = {
            'signature': re.compile(r'【.*?】'),
            'phone_basic': re.compile(r'1[3-9]\d{9}'),
            'phone_bracket': re.compile(r'[（(]1[3-9]\d[）)]\d{8}'),
            'phone_international': re.compile(r'(?<!\d)00\d{11,13}(?!\d)'),
            'phone_landline': re.compile(r'(?<!\d)0\d{2,3}[-\s]?\d{7,8}(?!\d)'),
            'phone_400': re.compile(r'(?<!\d)[48]00[-\s]?\d{3}[-\s]?\d{4}(?!\d)'),
            'phone_95': re.compile(r'(?<!\d)95\d{3,4}(?!\d)'),
            'amount_yuan': re.compile(r'(\d+\.?\d*)\s*元'),
            'amount_wan': re.compile(r'(\d+\.?\d*)\s*万'),
            'amount_rmb': re.compile(r'￥(\d+\.?\d*)'),
            'amount_quota': re.compile(r'(?:额度|金额|借款|款项|额度最高)[:：]?\s*(\d{3,8}(?:\.\d+)?)'),
            'amount_available': re.compile(r'(?:可提|到账)[:：]?\s*(\d{3,8}(?:\.\d+)?)'),
            'url_http': re.compile(r'(https?://[a-zA-Z0-9][a-zA-Z0-9\-\.]*[a-zA-Z0-9](?:/[a-zA-Z0-9\-_]*)*)'),
            'url_www': re.compile(
                r'(?<![a-zA-Z0-9])((?:www\.)?[a-zA-Z0-9][a-zA-Z0-9\-]*\.[a-zA-Z]{2,}(?:/[a-zA-Z0-9\-_]*)*)'),
            'url_click': re.compile(
                r'(?:点击?|点|查看|访问)\s*((?:www\.)?[a-zA-Z0-9][a-zA-Z0-9\-]*\.[a-zA-Z]{2,}(?:/[a-zA-Z0-9\-_]*)*)')
        }

    @staticmethod
    def is_valid_amount(amount_str: str, unit: str) -> bool:
        """验证金额有效性"""
        try:
            amount = float(amount_str)
            if unit == '万':
                return 0.1 <= amount <= 500  # 合理范围: 0.1万-500万
            elif unit == '元':
                return 10 <= amount <= 100000000  # 合理范围: 10元-1亿元
            return True
        except ValueError:
            return False

    @staticmethod
    def is_valid_phone(phone: str) -> bool:
        """验证电话号码有效性"""
        phone = str(phone).strip()
        if len(phone) < 7 or len(phone) > 20:
            return False

        patterns = [
            r'^00\d{11,13}$',  # 国际号码
            r'^1[3-9]\d{9}$',  # 手机号
            r'^0\d{2,3}\d{7,8}$',  # 固话
            r'^[48]00\d{7}$',  # 400/800
            r'^95\d{3,4}$'  # 95号码
        ]

        return any(re.match(pattern, phone) for pattern in patterns)

    @staticmethod
    def is_valid_url(url: str) -> bool:
        """URL验证"""
        if len(url) < 4 or len(url) > 200:
            return False
        if re.match(r'^\d+$', url):
            return False

        exclude_patterns = [
            r'^(点击|查看|访问|打开|进入|点此|链接|网址|详情|更多|立即)$',
            r'^\d{4}-\d{2}-\d{2}$',
            r'^\d+[:：]\d+$',
            r'^[\u4e00-\u9fff]+$',
        ]

        for pattern in exclude_patterns:
            if re.match(pattern, url):
                return False

        if '.' not in url:
            return False

        return re.search(r'[a-zA-Z].*\.[a-zA-Z]', url) is not None

    def extract_signature(self, text: str) -> str:
        """提取短信签名"""
        match = self._patterns['signature'].search(text)
        return match.group() if match else "无签名"

    def extract_amounts(self, text: str) -> List[str]:
        """提取金额信息"""
        amounts = set()
        # 处理"万元"格式
        wan_matches = re.finditer(r'(\d+\.?\d*)\s*万\s*元?', text)
        for match in wan_matches:
            amount = match.group(1)
            if self.is_valid_amount(amount, '万'):
                amounts.add(f"{amount}万元")

        # 处理"元"格式
        yuan_matches = re.finditer(r'(\d+\.?\d*)\s*元', text)
        for match in yuan_matches:
            amount = match.group(1)
            if self.is_valid_amount(amount, '元'):
                amounts.add(f"{amount}元")

        # 处理人民币符号格式
        rmb_matches = re.finditer(r'[￥¥](\d+\.?\d*)', text)
        for match in rmb_matches:
            amount = match.group(1)
            if self.is_valid_amount(amount, '元'):
                amounts.add(f"{amount}元")

        # 处理额度/金额格式
        quota_matches = re.finditer(r'(?:额度|金额|借款)[:：]?\s*(\d{3,8}(?:\.\d+)?)', text)
        for match in quota_matches:
            amount = match.group(1)
            if self.is_valid_amount(amount, '元'):
                amounts.add(f"{amount}元")

        return list(amounts)


    def extract_phones(self, text: str) -> List[str]:
            """提取电话号码"""
            phones = set()
            temp_text = re.sub(r'\d+\.?\d*\s*[元万]', '[AMOUNT]', text)

            patterns = [
                self._patterns['phone_bracket'],
                self._patterns['phone_international'],
                self._patterns['phone_basic'],
                self._patterns['phone_landline'],
                self._patterns['phone_400'],
                self._patterns['phone_95']
            ]

            for pattern in patterns:
                for match in pattern.finditer(temp_text):
                    phone = re.sub(r'[-\s（）()]', '', match.group())
                    if self.is_valid_phone(phone):
                        phones.add(phone)

            return list(phones)

    def extract_urls(self, text: str) -> List[str]:
        """提取URL"""
        processed_text = text
        processed_text = re.sub(r'http//(?!/)([a-zA-Z0-9])', r'http://\1', processed_text)
        processed_text = re.sub(r'https//(?!/)([a-zA-Z0-9])', r'https://\1', processed_text)
        processed_text = re.sub(r'([a-zA-Z0-9]+),([a-zA-Z]{2,})', r'\1.\2', processed_text)
        processed_text = processed_text.replace('：', ':').replace('／', '/').replace('．', '.')

        all_urls = []

        for pattern_name in ['url_http', 'url_www', 'url_click']:
            matches = self._patterns[pattern_name].finditer(processed_text)
            for match in matches:
                url = match.group(1) if match.groups() else match.group(0)
                url = url.strip()
                url = re.sub(r'[,，。.!！?？；;：:\s]+$', '', url)
                url = re.sub(r'^[,，。.!！?？；;：:\s]+', '', url)

                if url and self.is_valid_url(url):
                    all_urls.append(url)

        # 智能去重
        filtered_urls = []
        for url in all_urls:
            is_contained = False
            for other_url in all_urls:
                if url != other_url and url in other_url:
                    is_contained = True
                    break
            if not is_contained:
                filtered_urls.append(url)

        return list(dict.fromkeys(filtered_urls))

    def extract_fraud_keywords(self, text: str) -> Dict[str, List[str]]:
        """提取诈骗关键词"""
        fraud_categories = defaultdict(list)

        for category, keywords in Config.FRAUD_KEYWORDS.items():
            for keyword in keywords:
                if keyword in text:
                    fraud_categories[category].append(keyword)

        return dict(fraud_categories)

    def extract_verification_codes(self, text: str) -> List[str]:
        """提取验证码"""
        patterns = [
            r'(验证码|校验码|验证码是|校验码是)[:：]?\s*(\d{4,6})',
            r'(验证码|校验码)[:：]?\s*为\s*(\d{4,6})',
            r'(\d{4,6})\s*(验证码|校验码)'
        ]
        codes = set()

        for pattern in patterns:
            for match in re.finditer(pattern, text):
                code = match.group(2) if match.groups() > 1 else match.group(1)
                if code.isdigit() and 4 <= len(code) <= 6:
                    codes.add(code)

        return list(codes)

    def extract_all_features(self, text: str) -> ExtractedFeatures:
        """提取所有特征"""
        return ExtractedFeatures(
            signature=self.extract_signature(text),
            amounts=self.extract_amounts(text),
            phones=self.extract_phones(text),
            urls=self.extract_urls(text),
            verification_codes=self.extract_verification_codes(text),
            fraud_keywords=self.extract_fraud_keywords(text)
        )

class RiskAssessor:
    """终极风险评估器"""

    def __init__(self):
        self.logger = Logger.setup_logger()

    def assess_signature_risk(self, signature: str) -> Tuple[str, str]:
        """评估签名风险"""
        if signature == "无签名":
            return "无签名", "无签名"

        for pattern_str in Config.OFFICIAL_SIGNATURE_PATTERNS:
            pattern = Config.get_compiled_pattern(pattern_str)
            if pattern.fullmatch(signature):
                return "已备案签名", f"官方签名:{signature}"

        return "未备案签名", f"签名未备案:{signature}"

    def assess_phone_risk(self, phones: List[str], content: str) -> Tuple[float, str]:
        """评估电话风险"""
        if not phones:
            return 0, ""

        weights = Config.RISK_WEIGHTS["phones"]

        # 检查伪装号码
        disguise_pattern = Config.get_compiled_pattern(r'[（(]1[3-9]\d[）)]\d{8}')
        if disguise_pattern.search(content):
            return weights["伪装普通号码"], f"伪装普通号码({weights['伪装普通号码']})"

        # 统计号码类型
        phone_stats = {'mobile': 0, 'international': 0, 'zero_start': 0}

        for phone in phones:
            phone = re.sub(r'\D', '', str(phone))
            if phone.startswith('00'):
                phone_stats['international'] += 1
            elif phone.startswith('0') and len(phone) == 11:
                phone_stats['zero_start'] += 1
            elif re.fullmatch(r'1[3-9]\d{9}', phone):
                phone_stats['mobile'] += 1

        # 按优先级返回风险
        if phone_stats['international'] > 0:
            return weights["国际号码"], f"国际号码({weights['国际号码']})"
        elif phone_stats['zero_start'] > 0:
            return weights["0开头号码"], f"0开头号码({weights['0开头号码']})"
        elif phone_stats['mobile'] >= 2:
            return weights["双普通号码"], f"双普通号码({weights['双普通号码']})"
        elif phone_stats['mobile'] == 1:
            return weights["普通号码"], f"普通号码({weights['普通号码']})"

        return 0, ""

    def calculate_risk_score(self, features: ExtractedFeatures, content: str) -> Tuple[float, str]:
        """计算风险分数"""
        risk_score = 0
        reasons = []

        # 1. 关键词风险
        for category, keywords in features.fraud_keywords.items():
            if keywords:
                weight = Config.RISK_WEIGHTS["keywords"].get(category, 1.0)
                count = min(len(keywords), 2)
                risk_score += weight * count
                reasons.append(f"{category}x{count}({weight}*{count})")

        # 2. 金额风险 - 修复后的版本
        if features.amounts:
            max_amount = 0
            for amt in features.amounts:
                try:
                    # 清理金额字符串
                    clean_amt = amt.replace(",", "").replace("，", "").strip()

                    # 分离数值和单位
                    if "万" in amt:
                        unit = "万"
                        num_str = clean_amt.replace("万", "").replace("元", "")
                    else:
                        unit = "元"
                        num_str = clean_amt.replace("元", "")

                    if not num_str:
                        continue

                    # 转换为浮点数
                    amount = float(num_str)

                    # 根据单位转换
                    if unit == "万":
                        amount *= 10000

                    max_amount = max(max_amount, amount)
                except (ValueError, AttributeError) as e:
                    self.logger.warning(f"无效金额格式: {amt}, 错误: {e}")
                    continue

            # 只处理有效金额
            if max_amount > 0:
                for threshold, weight in sorted(Config.RISK_WEIGHTS["amounts"], reverse=True):
                    if max_amount >= threshold:
                        risk_score += weight
                        reasons.append(f"金额>={threshold}元({weight})")
                        break

        # 3. 链接风险
        if features.urls:
            link_weight = Config.RISK_WEIGHTS["phones"]["包含链接"]
            risk_score += link_weight
            reasons.append(f"包含链接({link_weight})")

        # 4. 电话风险
        if features.phones:
            phone_score, phone_reason = self.assess_phone_risk(features.phones, content)
            risk_score += phone_score
            if phone_reason:
                reasons.append(phone_reason)

        return risk_score, " + ".join(reasons) if reasons else "无明显风险特征"

    def get_risk_level(self, risk_score: float) -> str:
        """根据风险分数确定风险等级"""
        if risk_score >= Config.RISK_THRESHOLDS["high"]:
            return "高风险"
        elif risk_score >= Config.RISK_THRESHOLDS["medium"]:
            return "中风险"
        elif risk_score >= Config.RISK_THRESHOLDS["low"]:
            return "低风险"
        else:
            return "无风险"

# ==================== 主分析器 ====================
class SMSFraudAnalyzer:
    """短信诈骗分析器主类"""

    def __init__(self):
        self.logger = setup_logger(name='sms_fraud_analyzer',
                                 log_file='sms_fraud_analysis.log')
        self.feature_extractor = FeatureExtractor()
        self.risk_assessor = RiskAssessor()
        self._analysis_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_stats = {
            'hits': 0,
            'misses': 0,
            'total': 0,
            'last_updated': time.time()
        }
        self.logger.info("短信诈骗风险识别系统初始化完成")

    def get_cache_stats(self) -> Dict[str, float]:
        """获取缓存统计信息"""
        stats = {
            'hits': self._cache_stats.get('hits', 0),
            'misses': self._cache_stats.get('misses', 0),
            'total': self._cache_stats.get('total', 0),
            'cache_size': len(self._analysis_cache),
            'hit_rate': 0.0
        }

        if stats['total'] > 0:
            stats['hit_rate'] = (stats['hits'] / stats['total']) * 100

        return stats

    def clear_cache(self) -> None:
        """清空缓存"""
        self._analysis_cache.clear()
        self._cache_stats = {'hits': 0, 'misses': 0, 'total': 0}
        self.logger.info("分析缓存已清空")

    def _manage_cache_size(self) -> None:
        """管理缓存大小，使用LRU策略"""
        if len(self._analysis_cache) > Config.CACHE_SETTINGS["cache_cleanup_threshold"]:
            # 按最后使用时间排序
            sorted_items = sorted(
                self._analysis_cache.items(),
                key=lambda x: x[1].get('last_used', 0),
                reverse=True
            )
            # 保留80%的缓存
            keep_count = int(Config.CACHE_SETTINGS["max_cache_size"] * 0.8)
            self._analysis_cache = dict(sorted_items[-keep_count:])
            self.logger.debug(f"缓存清理完成，保留 {keep_count} 条")

    def _get_from_cache(self, cache_key: str) -> Optional[Dict]:
        """安全地从缓存获取结果"""
        if not Config.CACHE_SETTINGS["enable_cache"]:
            return None

        self._cache_stats['total'] += 1
        if cache_key in self._analysis_cache:
            self._cache_stats['hits'] += 1
            self._analysis_cache[cache_key]['last_used'] = time.time()
            return self._analysis_cache[cache_key]['result']
        self._cache_stats['misses'] += 1
        return None

    def _store_in_cache(self, cache_key: str, result: Dict) -> None:
        """安全地存储结果到缓存"""
        if not Config.CACHE_SETTINGS["enable_cache"]:
            return

        self._analysis_cache[cache_key] = {
            'result': result,
            'last_used': time.time(),
            'created_at': time.time()
        }
        # 自动管理缓存大小
        if len(self._analysis_cache) > Config.CACHE_SETTINGS["max_cache_size"]:
            self._manage_cache_size()

    def analyze_single_sms(self, content: str, sender: str, case_id: str = "",
                           send_time: str = "") -> SMSAnalysisResult:
        """分析单条短信"""
        try:
            # 检查缓存
            cache_key = get_content_hash(content, sender)
            cached_result = self._get_from_cache(cache_key)

            if cached_result:
                # 使用缓存结果，但更新案件特定信息
                return SMSAnalysisResult(
                    案件编号=case_id or f"SMS_{int(time.time())}",
                    短信内容=content,
                    主叫号码=sender,
                    发送时间=send_time or datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    **{k: v for k, v in cached_result.items()
                       if k not in ['案件编号', '短信内容', '主叫号码', '发送时间']}
                )

            # 提取特征
            features = self.feature_extractor.extract_all_features(content)

            # 评估签名风险
            signature_risk, signature_reason = self.risk_assessor.assess_signature_risk(
                features.signature)

            # 计算总体风险分数
            risk_score, risk_reason = self.risk_assessor.calculate_risk_score(
                features, content)

            # 确定风险等级
            risk_level = self.risk_assessor.get_risk_level(risk_score)

            # 格式化结果
            result_data = {
                '短信签名': features.signature,
                '签名风险': signature_risk,
                '判断原因': risk_reason,
                '敏感词': '; '.join([
                    f"{cat}:{','.join(words)}"
                    for cat, words in features.fraud_keywords.items()
                ]) if features.fraud_keywords else "无",
                '提取金额': '; '.join(features.amounts) if features.amounts else "无",
                '提取电话': '; '.join(features.phones) if features.phones else "无",
                '提取URL': '; '.join(features.urls) if features.urls else "无",
                '验证码': '; '.join(features.verification_codes) if features.verification_codes else "无",
                '风险分数': round(risk_score, 2),
                '风险等级': risk_level
            }

            # 存入缓存
            self._store_in_cache(cache_key, result_data)

            return SMSAnalysisResult(
                案件编号=case_id or f"SMS_{int(time.time())}",
                短信内容=content,
                主叫号码=sender,
                发送时间=send_time or datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                **result_data
            )

        except Exception as e:
            self.logger.error(f"分析单条短信失败 - 案件编号: {case_id}, 错误: {e}")
            return SMSAnalysisResult(
                案件编号=case_id or f"ERROR_{int(time.time())}",
                短信内容=content[:500] if content else "空内容",
                主叫号码=sender,
                发送时间=send_time or datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                短信签名="分析失败",
                签名风险="系统错误",
                判断原因=f"分析异常: {type(e).__name__}",
                敏感词="无",
                提取金额="无",
                提取电话="无",
                提取URL="无",
                验证码="无",
                风险分数=0.0,
                风险等级="无法判断"
            )

    def read_excel_file(self, file_path: Path) -> pd.DataFrame:
        """读取Excel文件"""
        try:
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")

            df = pd.read_excel(file_path)
            self.logger.info(f"成功读取Excel文件: {file_path}, 共{len(df)}行数据")

            # 验证必需列
            required_columns = ['短信内容', '主叫号码']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"缺少必需的列: {missing_columns}")

            # 清理数据
            df['短信内容'] = df['短信内容'].fillna('').astype(str)
            df['主叫号码'] = df['主叫号码'].fillna('').astype(str)

            # 添加案件编号（如果没有）
            if '案件编号' not in df.columns:
                df['案件编号'] = [f"SMS_{i + 1:06d}" for i in range(len(df))]

            # 添加发送时间（如果没有）
            if '发送时间' not in df.columns:
                df['发送时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 过滤空内容
            df = df[df['短信内容'].str.strip() != '']

            return df

        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {e}")
            raise

    def analyze_batch(self, df: pd.DataFrame, batch_size: int = 100) -> List[SMSAnalysisResult]:
        """批量分析短信"""
        results = []
        total_count = len(df)
        progress_bar = ProgressBar(total_count, "分析进度")

        self.logger.info(f"开始批量分析 {total_count} 条短信...")

        # 分批处理
        for i in range(0, total_count, batch_size):
            batch_df = df.iloc[i:i + batch_size]
            batch_results = []

            for _, row in batch_df.iterrows():
                result = self.analyze_single_sms(
                    content=str(row.get('短信内容', '')),
                    sender=str(row.get('主叫号码', '')),
                    case_id=str(row.get('案件编号', f'SMS_{i}')),
                    send_time=str(row.get('发送时间', ''))
                )
                batch_results.append(result)
                progress_bar.update(1)

            results.extend(batch_results)

            # 每处理完一批后进行垃圾回收
            if i % (batch_size * 5) == 0:
                gc.collect()

        # 输出缓存统计
        cache_stats = self.get_cache_stats()
        self.logger.info(f"缓存统计 - 命中率: {cache_stats['hit_rate']:.1f}%, "
                         f"缓存大小: {cache_stats['cache_size']}")

        self.logger.info(f"批量分析完成，共处理 {len(results)} 条短信")
        return results

    def export_results(self, results: List[SMSAnalysisResult], output_path: Path) -> None:
        """导出结果到Excel"""
        try:
            results_data = [asdict(result) for result in results]
            results_df = pd.DataFrame(results_data)

            # 生成统计摘要
            risk_stats = Counter(result.风险等级 for result in results)
            summary_data = []
            total_count = len(results)

            for level in ['高风险', '中风险', '低风险', '无风险']:
                count = risk_stats.get(level, 0)
                percentage = (count / total_count * 100) if total_count > 0 else 0
                summary_data.append({
                    '风险等级': level,
                    '数量': count,
                    '占比(%)': f'{percentage:.1f}%'
                })

            summary_df = pd.DataFrame(summary_data)

            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                results_df.to_excel(writer, sheet_name='详细分析结果', index=False)
                summary_df.to_excel(writer, sheet_name='统计摘要', index=False)

            self.logger.info(f"结果已成功导出到: {output_path}")

        except Exception as e:
            self.logger.error(f"导出Excel失败: {e}")
            raise

    def process_file(self, path_config: PathConfig, batch_size: int = 100) -> None:
        """处理文件"""
        try:
            # 读取数据
            input_path = path_config.get_input_path()
            self.logger.info(f"正在读取文件: {input_path}")
            df = self.read_excel_file(input_path)

            # 批量分析
            results = self.analyze_batch(df, batch_size)

            # 导出结果
            output_path = path_config.get_output_path()
            self.export_results(results, output_path)

            # 统计结果
            risk_stats = Counter(result.风险等级 for result in results)
            self.logger.info("=== 分析结果统计 ===")
            for level in ['高风险', '中风险', '低风险', '无风险']:
                count = risk_stats.get(level, 0)
                percentage = (count / len(results) * 100) if results else 0
                self.logger.info(f"{level}: {count}条 ({percentage:.1f}%)")

        except Exception as e:
            self.logger.error(f"处理文件失败: {e}")
            raise

# ==================== 交互式界面 ====================
class InteractiveInterface:
    """交互式用户界面"""

    def __init__(self):
        self.analyzer = SMSFraudAnalyzer()
        self.current_config = None

    def display_banner(self):
        """显示启动横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    短信诈骗风险识别系统                         ║
║                        Interactive v8.1.0                    ║
║                                                              ║
║  功能: 智能识别短信诈骗风险，支持批量分析和实时监控                  ║
║  作者: woods.tong                                             ║
║  日期: 2025-07-01                                             ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)

    def display_main_menu(self):
        """显示主菜单"""
        menu = """
────────────────── 主菜单 ───────────────────
1. 快速分析 (使用默认配置)
2. 自定义配置分析
3. 单条短信测试
4. 系统设置
0. 退出系统

        """
        print(menu)

    def get_user_choice(self, prompt: str = "请选择操作") -> str:
        """获取用户选择"""
        try:
            choice = input(f"\n{prompt} (输入数字): ").strip()
            return choice
        except KeyboardInterrupt:
            print("\n\n操作已取消")
            return "0"

    def quick_analysis(self):
        """快速分析"""
        print("\n=== 快速分析模式 ===")

        try:
            # 使用默认配置
            config = PathConfig()

            # 检查输入文件
            if not config.get_input_path().exists():
                print(f"❌ 输入文件不存在: {config.get_input_path()}")
                print(f"请将Excel文件放置在: {config.input_dir}")
                input("按回车键继续...")
                return

            # 执行分析
            print("正在执行快速分析...")
            results = self.analyzer.process_file(config)

            print("\n✅ 分析完成！")
            print(f"结果已保存到: {config.get_output_path()}")

        except Exception as e:
            print(f"❌ 分析失败: {e}")

        input("\n按回车键继续...")

    def custom_analysis(self):
        """自定义配置分析"""
        print("\n=== 自定义配置分析 ===")

        try:
            # 获取用户配置
            input_dir = input("输入数据目录 (回车使用默认 ./data): ").strip()
            input_file = input("输入文件名 (回车使用默认 sms_data.xlsx): ").strip()
            output_dir = input("输出目录 (回车使用默认 ./output): ").strip()

            # 创建配置
            config = PathConfig(
                input_dir=input_dir if input_dir else None,
                input_filename=input_file if input_file else None,
                output_dir=output_dir if output_dir else None
            )

            if not config.validate():
                print("❌ 配置验证失败!")
                input("按回车键继续...")
                return

            # 执行分析
            print("正在执行自定义分析...")
            results = self.analyzer.process_file(config)

            print("\n✅ 分析完成！")
            print(f"结果已保存到: {config.get_output_path()}")

        except Exception as e:
            print(f"❌ 分析失败: {e}")

        input("\n按回车键继续...")

    def single_message_test(self):
        """单条短信测试"""
        print("\n=== 单条短信测试 ===")

        try:
            content = input("请输入短信内容: ").strip()
            if not content:
                print("❌ 短信内容不能为空")
                input("按回车键继续...")
                return

            sender = input("请输入发送号码 (可选): ").strip() or "测试号码"

            print("\n正在分析...")
            result = self.analyzer.analyze_single_sms(content, sender)

            # 显示结果
            print("\n=== 分析结果 ===")
            print(f"短信签名: {result.短信签名}")
            print(f"签名风险: {result.签名风险}")
            print(f"风险等级: {result.风险等级}")
            print(f"风险分数: {result.风险分数}")
            print(f"判断原因: {result.判断原因}")
            print(f"敏感词: {result.敏感词}")
            print(f"提取金额: {result.提取金额}")
            print(f"提取电话: {result.提取电话}")
            print(f"提取URL: {result.提取URL}")
            print(f"分析耗时: {result.分析耗时}秒")

        except Exception as e:
            print(f"❌ 测试失败: {e}")

        input("\n按回车键继续...")

    def system_settings(self):
        """系统设置"""
        print("\n=== 系统设置 ===")

        settings_menu = """
        1. 清理缓存
        2. 查看缓存状态  
        3. 性能监控设置
        4. 返回主菜单
        """
        print(settings_menu)

        choice = self.get_user_choice("请选择设置选项")

        if choice == "1":
            self._clear_cache()
        elif choice == "2":
            self._view_cache_status()
        elif choice == "3":
            print("性能监控设置功能开发中...")

        input("\n按回车键继续...")

    def _clear_cache(self):
        """清理缓存"""
        try:
            self.analyzer.clear_cache()
            print("✅ 缓存清理完成")
        except Exception as e:
            print(f"❌ 缓存清理失败: {e}")

    def _view_cache_status(self):
        """查看缓存状态"""
        try:
            stats = self.analyzer.get_cache_stats()
            print(f"缓存条目数: {stats['cache_size']}")
            print(f"缓存命中率: {stats['hit_rate']:.1f}%")
            print(f"总请求数: {stats['total']}")
        except Exception as e:
            print(f"❌ 查看缓存状态失败: {e}")

    def run(self):
        """运行交互式界面"""
        self.display_banner()

        while True:
            try:
                self.display_main_menu()
                choice = self.get_user_choice()

                if choice == "0":
                    print("\n感谢使用短信诈骗风险识别系统！")
                    break
                elif choice == "1":
                    self.quick_analysis()
                elif choice == "2":
                    self.custom_analysis()
                elif choice == "3":
                    self.single_message_test()
                elif choice == "4":
                    self.view_history()
                elif choice == "5":
                    self.system_settings()
                else:
                    print("❌ 无效选择，请重新输入")
                    time.sleep(1)

            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                break
            except Exception as e:
                print(f"❌ 程序运行出错: {e}")
                input("按回车键继续...")

# ==================== 命令行接口 ====================
def setup_argument_parser() -> argparse.ArgumentParser:
    """设置命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="短信诈骗风险识别系统 v8.1.0",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python sms_fraud_analyzer.py                          # 交互式模式
  python sms_fraud_analyzer.py -i data/test.xlsx        # 快速分析
  python sms_fraud_analyzer.py -i data/test.xlsx -o output/ # 指定输出目录
  python sms_fraud_analyzer.py --single "测试短信内容"    # 单条测试
        """
    )

    parser.add_argument('-i', '--input',
                        help='输入Excel文件路径')
    parser.add_argument('-o', '--output',
                        help='输出目录路径')
    parser.add_argument('--input-dir',
                        help='输入文件目录')
    parser.add_argument('--output-dir',
                        help='输出文件目录')
    parser.add_argument('--single',
                        help='单条短信测试模式')
    parser.add_argument('--batch-size', type=int, default=100,
                        help='批处理大小 (默认: 100)')
    parser.add_argument('--no-cache', action='store_true',
                        help='禁用缓存')
    parser.add_argument('--clear-cache', action='store_true',
                        help='清理缓存后退出')
    parser.add_argument('-v', '--verbose', action='store_true',
                        help='详细输出模式')
    parser.add_argument('--version', action='version',
                        version='SMS Fraud Analyzer v8.1.0')

    return parser

def main():
    """主函数"""
    parser = setup_argument_parser()
    args = parser.parse_args()

    try:
        # 处理特殊参数
        if args.clear_cache:
            analyzer = SMSFraudAnalyzer()
            analyzer.clear_cache()
            print("缓存已清理")
            return

        # 单条测试模式
        if args.single:
            analyzer = SMSFraudAnalyzer()
            result = analyzer.analyze_single_sms(args.single, "命令行测试")

            print(f"短信内容: {result.短信内容}")
            print(f"风险等级: {result.风险等级}")
            print(f"风险分数: {result.风险分数}")
            print(f"判断原因: {result.判断原因}")
            return

        # 批量分析模式
        if args.input:
            config = PathConfig(
                input_dir=args.input_dir,
                input_filename=args.input,
                output_dir=args.output or args.output_dir
            )

            if args.no_cache:
                Config.CACHE_SETTINGS["enable_cache"] = False

            analyzer = SMSFraudAnalyzer()
            print("开始批量分析...")
            analyzer.process_file(config, batch_size=args.batch_size)
            print("分析完成！")
            return

        # 默认交互式模式
        interface = InteractiveInterface()
        interface.run()

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()