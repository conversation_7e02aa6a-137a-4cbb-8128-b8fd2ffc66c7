import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
from datetime import datetime, timedelta
import warnings
import pymysql
import json
from pathlib import Path

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False


class TelecomMonitor:
    """运营商监控系统 - 电信专项版 (简化版)"""

    def __init__(self):
        self.connection = None
        self.is_connected = False

        # 风险阈值配置
        self.risk_config = {
            '电信': {'color': '#0769B6'},
        }

        # 配色方案
        self.colors = {
            'primary': '#0769B6',
            'danger': '#E34234',
            'warning': '#F0BE38',
            'success': '#043474',
            'neutral': '#D7D1DC',
            'background': '#F8F9FA',
            'grid': '#E9ECEF',
            'text': '#2C3E50'
        }

        # 数据库配置
        self.db_config = {
            'host': '************',
            'port': 57861,
            'user': 'FzUser',
            'password': 'jZ4%fQ3}oI8(jH7)',
            'database': 'antiFraudPlatform',
            'charset': 'utf8mb4'
        }

        # 缓存配置
        self.cache_dir = Path("../cache")
        self.cache_dir.mkdir(exist_ok=True)
        self.cache_file = self.cache_dir / "fraud_cases_cache.json"

        # 图表导出配置
        self.export_dir = Path("/Users/<USER>/Documents/测试")
        self.export_dir.mkdir(exist_ok=True)

    def connect_database(self):
        """连接数据库"""
        try:
            # 先关闭现有连接
            if self.connection:
                try:
                    self.connection.close()
                except:
                    pass

            self.connection = pymysql.connect(
                **self.db_config,
                autocommit=True,  # 启用自动提交
                connect_timeout=10,  # 连接超时
                read_timeout=30,  # 读取超时
                write_timeout=30  # 写入超时
            )
            self.is_connected = True
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            self.is_connected = False
            return False

    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            try:
                self.connection.close()
                self.is_connected = False
                print("✅ 数据库连接已关闭")
            except Exception as e:
                print(f"❌ 关闭数据库连接失败: {e}")

    def get_data(self, days=30, use_cache=True):
        """获取数据，支持缓存机制"""
        # 检查缓存
        if use_cache and self.cache_file.exists():
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)

                cache_date = cache_data.get('cache_date')
                today = datetime.now().strftime('%Y-%m-%d')

                if cache_date == today:
                    print("📦 使用缓存数据")
                    df = pd.DataFrame(cache_data['data'])
                    if not df.empty:
                        df['日期'] = pd.to_datetime(df['日期'])
                    return df
            except Exception:
                pass

        # 查询数据库
        df = self._query_database(days)

        # 保存缓存
        if use_cache and df is not None and not df.empty:
            try:
                cache_data = {
                    'cache_date': datetime.now().strftime('%Y-%m-%d'),
                    'data': df.to_dict('records')
                }
                with open(self.cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, ensure_ascii=False, indent=2, default=str)
                print("💾 数据已缓存")
            except Exception:
                pass

        return df

    def _query_database(self, days=30):
        """查询数据库"""
        if not self.is_connected and not self.connect_database():
            return None

        try:
            today = datetime.now()
            end_date = (today - timedelta(days=1)).strftime('%Y-%m-%d')
            start_date = (today - timedelta(days=days + 1)).strftime('%Y-%m-%d')

            print(f"🔍 查询日期范围: {start_date} 到 {end_date}")

            # 简化查询，只查询电信数据
            query = f"""
            SELECT 
                DATE(insert_day) as date_col,
                COUNT(*) as telecom
            FROM anti_fraud_case_new 
            WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
              AND involved_amount >= 500000
              AND suspect_phone_info LIKE '%电信%'
            GROUP BY DATE(insert_day)
            ORDER BY DATE(insert_day)
            """

            df = pd.read_sql(query, self.connection)

            if not df.empty:
                df = df.rename(columns={'date_col': '日期', 'telecom': '电信'})
                df['日期'] = pd.to_datetime(df['日期'])
                print(f"✅ 查询成功，共 {len(df)} 条记录，电信案件总数: {df['电信'].sum()}")

                # 添加日期填充逻辑，确保所有日期都存在
                date_range = pd.date_range(start=start_date, end=end_date, freq='D')
                full_df = pd.DataFrame({'日期': date_range})
                df = full_df.merge(df, on='日期', how='left')
                # 正确处理NaN值
                df['电信'] = df['电信'].fillna(0).astype(int)

                print(f"📅 填充日期后，共 {len(df)} 条记录")
            else:
                print("⚠️ 查询结果为空")
                # 创建空的DataFrame但包含所有日期
                date_range = pd.date_range(start=start_date, end=end_date, freq='D')
                df = pd.DataFrame({'日期': date_range, '电信': 0})

            return df
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return None

    def display_chart(self):
        """显示图表"""
        print("\n📊 正在获取数据...")
        df = self.get_data(30)

        if df is None or df.empty:
            print("❌ 无法获取数据，无法显示图表")
            return

        self.create_chart(df)

    def create_chart(self, df, save_only=False):
        """创建监控图表"""
        if df is None or df.empty:
            print("⚠️ 无数据可绘制图表")
            return

        fig, ax = plt.subplots(figsize=(16, 9))
        fig.patch.set_facecolor(self.colors['background'])

        # 绘制趋势线
        for operator in df.columns:
            if operator == '日期' or operator not in self.risk_config:
                continue

            x_data = df['日期']
            y_data = df[operator]

            ax.plot(x_data, y_data,
                    color=self.colors['primary'], linewidth=4.0,
                    label=operator, marker='o', markersize=8)
            ax.fill_between(x_data, y_data,
                            alpha=0.15, color=self.colors['primary'])

        # 美化图表
        ax.set_title('电信涉诈案件监控图表(涉案金额 ≥ 50万元)',
                     fontsize=24, fontweight='bold', pad=20, color=self.colors['text'])
        ax.set_xlabel('日期', fontsize=14, color=self.colors['text'])
        ax.set_ylabel('案件数量', fontsize=14, color=self.colors['text'])

        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=3))

        plt.setp(ax.xaxis.get_majorticklabels(), rotation=0, ha='center', fontsize=18, color=self.colors['text'])
        if not df.empty:
            max_val = df.iloc[:, 1:].max().max()
            if max_val > 0:
                ax.set_ylim(0, max_val + 1)
        plt.setp(ax.yaxis.get_majorticklabels(), fontsize=18, color=self.colors['text'])

        ax.grid(True, alpha=0.3, color=self.colors['grid'], linewidth=0.8)
        ax.set_facecolor(self.colors['background'])

        legend = ax.legend(loc='upper right', frameon=True, fancybox=True, shadow=True,
                           fontsize=14, markerscale=1.5, handlelength=2.5, handletextpad=0.8)
        legend.get_frame().set_facecolor('white')
        legend.get_frame().set_alpha(0.95)
        legend.get_frame().set_linewidth(1.2)

        for spine in ax.spines.values():
            spine.set_visible(False)

        plt.tight_layout()

        if save_only:
            # 保存图表
            if self.export_chart():
                print("✅ 图表已保存")
            plt.close(fig)
        else:
            # 显示图表
            plt.show()

    def get_weekly_data(self, weeks=8):
        """获取周维度数据"""
        if not self.is_connected and not self.connect_database():
            return None

        try:
            # 计算8周前的日期
            today = datetime.now()
            end_date = (today - timedelta(days=1)).strftime('%Y-%m-%d')
            start_date = (today - timedelta(weeks=weeks, days=1)).strftime('%Y-%m-%d')

            print(f"🔍 查询周维度数据: {start_date} 到 {end_date}")

            query = f"""
            SELECT 
                YEARWEEK(insert_day, 1) as year_week,
                MIN(DATE(insert_day)) as week_start,
                MAX(DATE(insert_day)) as week_end,
                COUNT(DISTINCT DATE(insert_day)) as days_count,
                SUM(CASE WHEN suspect_phone_info LIKE '%电信%' THEN 1 ELSE 0 END) as telecom,
                SUM(CASE WHEN suspect_phone_info LIKE '%移动%' THEN 1 ELSE 0 END) as mobile,
                SUM(CASE WHEN suspect_phone_info LIKE '%联通%' THEN 1 ELSE 0 END) as unicom
            FROM anti_fraud_case_new 
            WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
              AND involved_amount >= 500000
            GROUP BY YEARWEEK(insert_day, 1)
            HAVING COUNT(DISTINCT DATE(insert_day)) >= 5
            ORDER BY YEARWEEK(insert_day, 1)
            """

            df = pd.read_sql(query, self.connection)

            # 添加详细调试信息
            print(f"📊 查询结果: {len(df)} 行数据")
            if not df.empty:
                print("📋 数据预览:")
                print(df[['year_week', 'week_start', 'days_count', 'telecom', 'mobile', 'unicom']])
                print(
                    f"📈 各运营商总数: 电信={df['telecom'].sum()}, 移动={df['mobile'].sum()}, 联通={df['unicom'].sum()}")

                df['week_start'] = pd.to_datetime(df['week_start'])
                df['week_label'] = df['week_start'].dt.strftime('%Y-%U周')
            else:
                print("⚠️ 查询结果为空")

            return df

        except Exception as e:
            print(f"❌ 周维度查询失败: {e}")
            return None

    def create_weekly_chart(self, df, save_only=False):
        """创建周维度柱状图"""
        if df is None or df.empty:
            print("⚠️ 无数据可绘制周维度图表")
            return

        # 只显示最近8周
        df = df.tail(8)

        fig, ax = plt.subplots(figsize=(16, 9))
        fig.patch.set_facecolor(self.colors['background'])

        # 准备数据
        weeks = df['week_label'].values
        operators = ['telecom', 'mobile', 'unicom']
        operator_labels = ['电信', '移动', '联通']
        colors = ['#0769AD', '#E60012', '#00A0E9']

        # 柱状图参数
        bar_width = 0.25
        index = np.arange(len(weeks))

        # 绘制柱状图
        for i, (operator, label) in enumerate(zip(operators, operator_labels)):
            values = df[operator].values

            ax.bar(index + i * bar_width, values,
                   bar_width, label=label, color=colors[i],
                   alpha=0.85, edgecolor='white', linewidth=1.2)

        # 美化图表
        ax.set_title('三大运营商涉诈案件周维度统计(涉案金额 ≥ 50万元)',
                     fontsize=24, fontweight='bold', pad=20, color=self.colors['text'])
        ax.set_xlabel('周次', fontsize=14, color=self.colors['text'])
        ax.set_ylabel('案件数量', fontsize=14, color=self.colors['text'])

        # 设置横坐标
        ax.set_xticks(index + bar_width)
        ax.set_xticklabels(weeks, fontsize=12, color=self.colors['text'], rotation=0)

        # 设置纵坐标
        plt.setp(ax.yaxis.get_majorticklabels(), fontsize=12, color=self.colors['text'])

        # 网格和样式
        ax.grid(True, alpha=0.3, color=self.colors['grid'], linewidth=0.8, axis='y')
        ax.set_facecolor(self.colors['background'])

        # 图例
        legend = ax.legend(loc='upper right', frameon=True, fancybox=True, shadow=True,
                           fontsize=14, markerscale=1.5)
        legend.get_frame().set_facecolor('white')
        legend.get_frame().set_alpha(0.95)

        for spine in ax.spines.values():
            spine.set_visible(False)

        plt.tight_layout()

        if save_only:
            # 只保存，不显示
            if self.export_weekly_chart():
                print("✅ 周维度图表已保存")
            plt.close(fig)
        else:
            # 显示图表
            plt.show()

    def export_weekly_chart(self, filename=None):
        """导出周维度图表到文件"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"三大运营商周维度统计_{timestamp}.png"

        filepath = self.export_dir / filename

        try:
            plt.savefig(filepath, dpi=300, bbox_inches='tight',
                        facecolor='white', edgecolor='none')
            print(f"📁 周维度图表已保存到: {filepath}")
            return True
        except Exception as e:
            print(f"❌ 周维度图表保存失败: {e}")
            return False

    def save_weekly_chart_only(self):
        """仅保存周维度图表"""
        print("\n📊 正在获取周维度数据...")
        df = self.get_weekly_data(8)

        if df is None or df.empty:
            print("❌ 无法获取周维度数据，无法保存图表")
            return

        # 创建图表并保存
        self.create_weekly_chart(df, save_only=True)

    def display_weekly_chart(self):
        """显示周维度图表"""
        print("\n📊 正在获取周维度数据...")
        df = self.get_weekly_data(8)

        if df is None or df.empty:
            print("❌ 无法获取周维度数据，无法显示图表")
            return

        self.create_weekly_chart(df)

    def export_chart(self, filename=None):
        """导出图表到文件"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"电信(>50w)涉诈案情统计_{timestamp}.png"

        filepath = self.export_dir / filename

        try:
            plt.savefig(filepath, dpi=300, bbox_inches='tight',
                        facecolor='white', edgecolor='none')
            print(f"📁 图表已保存到: {filepath}")
            return True
        except Exception as e:
            print(f"❌ 图表保存失败: {e}")
            return False

    def save_chart_only(self):
        """仅保存图表"""
        print("\n📊 正在获取数据...")
        df = self.get_data(30)

        if df is None or df.empty:
            print("❌ 无法获取数据，无法保存图表")
            return

        # 使用统一的 create_chart 方法，传入 save_only=True
        self.create_chart(df, save_only=True)

    def display_basic_statistics(self):
        """显示基础统计信息"""
        print("\n" + "=" * 80)
        print("📊 电信反诈基础统计")
        print("=" * 80)

        # 获取30天数据进行统计
        df = self.get_data(30)
        if df is None or df.empty:
            print("❌ 无法获取数据")
            return

        # 总体统计
        telecom_data = df['电信'].values
        total_cases = int(telecom_data.sum())
        daily_avg = total_cases / len(telecom_data)
        max_daily = int(telecom_data.max())
        min_daily = int(telecom_data.min())

        print(f"📊 案件总数: {total_cases}起")
        print(f"📊 日均案件: {daily_avg:.1f}起")
        print(f"📊 最高单日: {max_daily}起")
        print(f"📊 最低单日: {min_daily}起")

        # 近一周统计
        stats = self.get_basic_statistics(7)
        if stats:
            print(f"\n📊 近一周统计:")
            print(f"📅 统计期间: {stats['period']}")

            # 显示每日统计
            daily_df = stats['daily_stats']
            if not daily_df.empty:
                print("\n📈 每日案件统计:")
                print("-" * 60)
                print(f"{'日期':<12} {'总案件数':<10} {'电信案件数':<10}")
                print("-" * 60)

                total_telecom = 0
                for _, row in daily_df.iterrows():
                    date_str = row['date_col'].strftime('%Y-%m-%d') if hasattr(row['date_col'], 'strftime') else str(row['date_col'])
                    total_cases = int(row['total_cases'])
                    telecom_cases = int(row['telecom_cases'])
                    total_telecom += telecom_cases
                    print(f"{date_str:<12} {total_cases:<10} {telecom_cases:<10}")

                # 计算平均值
                avg_telecom = total_telecom / len(daily_df) if len(daily_df) > 0 else 0
                print("-" * 60)
                print(f"📊 近一周电信案件总数: {total_telecom}起")
                print(f"📊 近一周电信案件平均: {avg_telecom:.1f}起/天")

            # 显示不同金额阈值统计
            amount_df = stats['amount_stats']
            if not amount_df.empty:
                print("\n💰 不同金额阈值统计:")
                print("-" * 60)
                print(f"{'金额范围':<12} {'总案件数':<10} {'电信案件数':<10}")
                print("-" * 60)

                for _, row in amount_df.iterrows():
                    amount_range = row['amount_range']
                    total_cases = int(row['total_cases'])
                    telecom_cases = int(row['telecom_cases'])
                    print(f"{amount_range:<12} {total_cases:<10} {telecom_cases:<10}")

        print("=" * 80)

    def get_basic_statistics(self, days=7):
        """获取基础统计数据"""
        if not self.is_connected and not self.connect_database():
            return None

        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

            # 获取近一周每日案件数量
            daily_query = f"""
                    SELECT 
                        DATE(insert_day) as date_col,
                        COUNT(*) as total_cases,
                        SUM(CASE WHEN suspect_phone_info LIKE '%电信%' THEN 1 ELSE 0 END) as telecom_cases
                    FROM anti_fraud_case_new 
                    WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
                      AND involved_amount >= 500000
                    GROUP BY DATE(insert_day)
                    ORDER BY DATE(insert_day)
                    """

            daily_df = pd.read_sql(daily_query, self.connection)

            # 获取不同金额阈值的案件数量
            amount_query = f"""
                    SELECT 
                        '50万以上' as amount_range,
                        COUNT(*) as total_cases,
                        SUM(CASE WHEN suspect_phone_info LIKE '%电信%' THEN 1 ELSE 0 END) as telecom_cases
                    FROM anti_fraud_case_new 
                    WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
                      AND involved_amount >= 500000

                    UNION ALL

                    SELECT 
                        '100万以上' as amount_range,
                        COUNT(*) as total_cases,
                        SUM(CASE WHEN suspect_phone_info LIKE '%电信%' THEN 1 ELSE 0 END) as telecom_cases
                    FROM anti_fraud_case_new 
                    WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
                      AND involved_amount >= 1000000

                    UNION ALL

                    SELECT 
                        '1000万以上' as amount_range,
                        COUNT(*) as total_cases,
                        SUM(CASE WHEN suspect_phone_info LIKE '%电信%' THEN 1 ELSE 0 END) as telecom_cases
                    FROM anti_fraud_case_new 
                    WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
                      AND involved_amount >= 10000000
                    """

            amount_df = pd.read_sql(amount_query, self.connection)

            return {
                'daily_stats': daily_df,
                'amount_stats': amount_df,
                'period': f"{start_date} 至 {end_date}"
            }

        except Exception as e:
            print(f"❌ 获取基础统计失败: {e}")
            return None

    def get_case_details(self, amount_threshold=500000, limit=20):
        """获取案件详情"""
        if not self.is_connected and not self.connect_database():
            return None

        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

            query = f"""
                    SELECT 
                        case_number as '案件编号',
                        insert_day as '入库时间',
                        involved_amount as '涉案金额',
                        occurrence_area as '发案地区'
                    FROM anti_fraud_case_new 
                    WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
                      AND involved_amount >= {amount_threshold}
                      AND suspect_phone_info LIKE '%电信%'
                    ORDER BY insert_day DESC, involved_amount DESC
                    LIMIT {limit}
                    """

            df = pd.read_sql(query, self.connection)
            return df

        except Exception as e:
            print(f"❌ 获取案件详情失败: {e}")
            return None

    def display_case_details(self):
        """显示案件详情"""
        print("\n📋 案件详情查看")
        print("=" * 50)
        print("请选择金额阈值：")
        print("【1】50万以上")
        print("【2】100万以上")
        print("【3】1000万以上")
        print("=" * 50)

        while True:
            choice = input("🔍 请输入选择: ").strip()

            if choice == '1':
                threshold = 500000
                threshold_text = "50万以上"
                break
            elif choice == '2':
                threshold = 1000000
                threshold_text = "100万以上"
                break
            elif choice == '3':
                threshold = 10000000
                threshold_text = "1000万以上"
                break
            else:
                print("❌ 无效选择，请重新输入")
                continue

        print(f"\n📊 正在查询{threshold_text}电信案件详情...")

        df = self.get_case_details(threshold, 20)
        if df is None or df.empty:
            print(f"❌ 未找到{threshold_text}的电信案件")
            return

        print(f"\n📋 {threshold_text}电信案件详情 (近7天，最多20条):")
        print("=" * 100)
        print(f"{'案件编号':<20} {'入库时间':<20} {'涉案金额(万元)':<15} {'发案地区':<20}")
        print("=" * 100)

        for _, row in df.iterrows():
            case_number = str(row['案件编号'])[:18] + ".." if len(str(row['案件编号'])) > 18 else str(row['案件编号'])
            insert_time = str(row['入库时间'])[:19] if len(str(row['入库时间'])) > 19 else str(row['入库时间'])
            amount_wan = float(row['涉案金额']) / 10000
            operator_info = str(row['发案地区'])[:18] + ".." if len(str(row['发案地区'])) > 18 else str(row['发案地区'])

            print(f"{case_number:<20} {insert_time:<20} {amount_wan:<15.1f} {operator_info:<20}")

        print("=" * 100)
        print(f"📊 共查询到 {len(df)} 条记录")

    def clear_cache(self):
        """清除缓存"""
        try:
            if self.cache_file.exists():
                self.cache_file.unlink()
                print("✅ 缓存已清除")
            else:
                print("⚠️ 缓存文件不存在")
        except Exception as e:
            print(f"❌ 清除缓存失败: {e}")

    def main_menu(self):
        """主菜单"""
        while True:
            print("\n" + "=" * 60)
            print("🛡️  电信反诈监控系统")
            print("=" * 60)
            print("请选择功能：")
            print("【1】显示日维度图表")
            print("【2】显示周维度图表")
            print("【3】保存日维度图表")
            print("【4】保存周维度图表")
            print("【5】基础统计")
            print("【6】案件详情")
            print("【7】清除缓存")
            print("【8】退出系统")
            print("=" * 60)

            try:
                choice = input("🔍 请输入选择: ").strip()

                if choice == '1':
                    self.display_chart()
                elif choice == '2':
                    self.display_weekly_chart()
                elif choice == '3':
                    self.save_chart_only()
                elif choice == '4':
                    self.save_weekly_chart_only()
                elif choice == '5':
                    self.display_basic_statistics()
                elif choice == '6':
                    self.display_case_details()
                elif choice == '7':
                    self.clear_cache()
                elif choice == '8':
                    print("👋 系统已退出")
                    break
                else:
                    print("❌ 无效选择，请重新输入")

            except KeyboardInterrupt:
                print("\n\n👋 用户中断，系统已退出")
                break
            except Exception as e:
                print(f"❌ 系统错误: {e}")

    def __del__(self):
        """析构函数 - 确保资源释放"""
        self.close_connection()


def main():
    """主函数"""
    monitor = TelecomMonitor()

    try:
        monitor.main_menu()
    except KeyboardInterrupt:
        print("\n\n👋 程序已中断")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
    finally:
        monitor.close_connection()


if __name__ == "__main__":
    main()