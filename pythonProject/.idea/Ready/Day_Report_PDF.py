import pandas as pd
import matplotlib.pyplot as plt
from sqlalchemy import create_engine
from datetime import datetime, timedelta
import os
import warnings
import sys
from urllib.parse import quote_plus
from sqlalchemy.sql import text

# 禁用警告
warnings.filterwarnings('ignore')

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 3306,
    'user': 'FZUser',
    'password': 'fz@20250324',
    'database': 'antiFraudPlatform',
    'charset': 'utf8mb4',
    'connect_timeout': 10
}

# 全局样式配置
plt.style.use('seaborn')
plt.rcParams.update({
    'font.family': 'Microsoft YaHei',
    'axes.unicode_minus': False,
    'grid.color': '#eeeeee',
    'grid.linestyle': '--',
    'grid.alpha': 0.7
})


class DatabaseManager:
    """数据库连接管理器"""

    def __init__(self):
        self.engine = self._create_engine()

    def _create_engine(self):
        """创建数据库引擎"""
        try:
            encoded_password = quote_plus(DB_CONFIG['password'])
            connection_string = (
                f"mysql+pymysql://{DB_CONFIG['user']}:{encoded_password}@"
                f"{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"
                f"?charset={DB_CONFIG['charset']}"
            )
            engine = create_engine(
                connection_string,
                pool_pre_ping=True,
                pool_recycle=3600,
                connect_args={'connect_timeout': DB_CONFIG['connect_timeout']}
            )

            # 修改后的测试连接方式
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            return engine
        except Exception as e:
            print(f"数据库连接失败: {str(e)}")
            return None

    def execute_query(self, query, params=None):
        """执行SQL查询"""
        try:
            if self.engine is None:
                self.engine = self._create_engine()
            return pd.read_sql(query, self.engine, params=params)

        except Exception as e:
            print(f"执行查询失败: {str(e)}")
            return pd.DataFrame()


class CaseDataAnalyzer:
    """案件数据分析模块"""

    def __init__(self, db_manager):
        self.db = db_manager

    def get_case_summary(self, report_date):
        """获取案件总体数据"""
        query = """
        SELECT 
            COUNT(*) AS total_cases,
            IFNULL(SUM(involved_amount), 0) AS total_amount,
            COUNT(DISTINCT suspect_phone_number) AS telecom_cases,
            SUM(CASE WHEN involved_amount > 500000 THEN 1 ELSE 0 END) AS high_amount_cases_50w,
            SUM(CASE WHEN involved_amount > 1000000 THEN 1 ELSE 0 END) AS high_amount_cases_100w,
            SUM(CASE WHEN involved_amount > 10000000 THEN 1 ELSE 0 END) AS high_amount_cases_1000w
        FROM anti_fraud_case_new 
        WHERE DATE(occurrence_time) = %s
        """
        return self._safe_query(query, (report_date,), default={
            'total_cases': 0, 'total_amount': 0, 'telecom_cases': 0,
            'high_amount_cases_50w': 0, 'high_amount_cases_100w': 0, 'high_amount_cases_1000w': 0
        })

    def get_app_cases(self, report_date):
        """获取涉诈APP数据"""
        query = """
        SELECT 
            COUNT(DISTINCT app_name) AS app_count,
            COUNT(*) AS case_count,
            IFNULL(SUM(involved_amount), 0) AS total_amount
        FROM anti_fraud_case_new 
        WHERE DATE(occurrence_time) = %s AND app_name IS NOT NULL AND app_name != ''
        """
        return self._safe_query(query, (report_date,), default={
            'app_count': 0, 'case_count': 0, 'total_amount': 0
        })

    def get_case_distribution(self, report_date):
        """获取案件分布数据"""
        query = """
        SELECT
            SUM(CASE WHEN url IS NOT NULL AND url != '无' THEN 1 ELSE 0 END) AS url_cases,
            SUM(CASE WHEN app_name IS NOT NULL AND app_name != '' AND app_name != '无' THEN 1 ELSE 0 END) AS app_cases,
            SUM(CASE WHEN suspect_phone_number IS NOT NULL AND suspect_phone_number != '' THEN 1 ELSE 0 END) AS call_sms_cases,
            SUM(CASE WHEN suspect_phone_number REGEXP '^(00|\\\\+)' THEN 1 ELSE 0 END) AS intl_number_cases,
            COUNT(DISTINCT suspect_phone_number) AS operator_cases,
            COUNT(DISTINCT CASE WHEN suspect_phone_info LIKE '%%电信%%' THEN suspect_phone_number END) AS telecom_cases,
            CASE WHEN COUNT(DISTINCT suspect_phone_number) > 0 THEN
                COUNT(DISTINCT CASE WHEN suspect_phone_info LIKE '%%电信%%' THEN suspect_phone_number END) / 
                COUNT(DISTINCT suspect_phone_number) * 100
            ELSE 0 END AS telecom_percentage
        FROM anti_fraud_case_new
        WHERE DATE(occurrence_time) = %s
        """
        return self._safe_query(query, (report_date,), default={
            'url_cases': 0, 'app_cases': 0, 'call_sms_cases': 0,
            'intl_number_cases': 0, 'operator_cases': 0,
            'telecom_cases': 0, 'telecom_percentage': 0
        })

    def get_operator_distribution(self, report_date):
        """获取运营商分布数据"""
        query = """
        SELECT
            COUNT(DISTINCT CASE WHEN suspect_phone_info LIKE '%%电信%%' THEN suspect_phone_number END) AS telecom_cases,
            COUNT(DISTINCT CASE WHEN suspect_phone_info LIKE '%%联通%%' THEN suspect_phone_number END) AS unicom_cases,
            COUNT(DISTINCT CASE WHEN suspect_phone_info LIKE '%%移动%%' THEN suspect_phone_number END) AS mobile_cases,
            COUNT(DISTINCT CASE WHEN suspect_phone_info LIKE '%%广电%%' THEN suspect_phone_number END) AS broadcast_cases,
            COUNT(DISTINCT CASE WHEN suspect_phone_info LIKE '%%虚商%%' THEN suspect_phone_number END) AS virtual_cases
        FROM anti_fraud_case_new
        WHERE DATE(occurrence_time) = %s
        """
        return self._safe_query(query, (report_date,), default={
            'telecom_cases': 0, 'unicom_cases': 0, 'mobile_cases': 0,
            'broadcast_cases': 0, 'virtual_cases': 0
        })

    def get_major_cases(self, report_date):
        """获取重大案件数据"""
        query = """
        SELECT 
            SUM(CASE WHEN involved_amount > 500000 THEN 1 ELSE 0 END) AS high_amount_cases,
            SUM(CASE WHEN involved_amount > 500000 AND suspect_phone_number IS NOT NULL THEN 1 ELSE 0 END) AS operator_related_cases,
            IFNULL(SUM(CASE WHEN suspect_phone_info LIKE '%%电信%%' THEN involved_amount ELSE 0 END), 0) AS telecom_amount,
            SUM(CASE WHEN involved_amount > 500000 AND suspect_phone_info LIKE '%%电信%%' THEN 1 ELSE 0 END) AS telecom_cases_50w,
            SUM(CASE WHEN involved_amount > 1000000 AND suspect_phone_info LIKE '%%电信%%' THEN 1 ELSE 0 END) AS telecom_cases_100w,
            SUM(CASE WHEN involved_amount > 10000000 AND suspect_phone_info LIKE '%%电信%%' THEN 1 ELSE 0 END) AS telecom_cases_1000w
        FROM anti_fraud_case_new
        WHERE DATE(occurrence_time) = %s
        """
        return self._safe_query(query, (report_date,), default={
            'high_amount_cases': 0, 'operator_related_cases': 0, 'telecom_amount': 0,
            'telecom_cases_50w': 0, 'telecom_cases_100w': 0, 'telecom_cases_1000w': 0
        })

    def get_top_apps(self, report_date, limit=10):
        """获取TOP涉案APP"""
        query = """
        SELECT 
            app_name,
            COUNT(*) AS case_count,
            IFNULL(SUM(involved_amount), 0) AS amount,
            COUNT(*) / (SELECT COUNT(*) FROM anti_fraud_case_new WHERE DATE(occurrence_time) = %s AND app_name IS NOT NULL) * 100 AS percentage
        FROM anti_fraud_case_new
        WHERE DATE(occurrence_time) = %s AND app_name IS NOT NULL AND app_name != ''
        GROUP BY app_name
        ORDER BY case_count DESC
        LIMIT %s
        """
        return self._safe_query(query, (report_date, report_date, limit), default=pd.DataFrame())

    def _safe_query(self, query, params, default):
        """安全执行查询"""
        try:
            df = self.db.execute_query(query, params)
            if not df.empty:
                return df.iloc[0].to_dict() if isinstance(default, dict) else df
        except Exception as e:
            print(f"查询执行错误: {str(e)}")
        return default


class ReportGenerator:
    """报告生成模块"""

    def __init__(self, analyzer):
        self.analyzer = analyzer

    def generate_simple_report(self, report_date, output_dir='/Users/<USER>/Documents/测试'):
        """生成简化版PDF报告"""
        os.makedirs(output_dir, exist_ok=True)

        # 获取数据
        case_summary = self.analyzer.get_case_summary(report_date)
        app_cases = self.analyzer.get_app_cases(report_date)
        case_dist = self.analyzer.get_case_distribution(report_date)
        operator_dist = self.analyzer.get_operator_distribution(report_date)
        major_cases = self.analyzer.get_major_cases(report_date)

        # 生成PDF报告
        return self._generate_pdf_report(report_date, case_summary, app_cases,
                                       case_dist, operator_dist, major_cases, output_dir)

    def _generate_pdf_report(self, report_date, case_summary, app_cases, case_dist, operator_dist, major_cases, output_dir):
        """生成PDF报告"""
        try:
            # 导入reportlab库
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib import colors
            from reportlab.lib.enums import TA_CENTER

            # 设置PDF文件路径
            report_path = os.path.join(output_dir, f'case_report_{report_date}.pdf')

            # 创建PDF文档
            doc = SimpleDocTemplate(report_path, pagesize=A4)
            story = []

            # 获取样式
            styles = getSampleStyleSheet()

            # 定义样式
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=TA_CENTER,
                fontName='Helvetica-Bold'
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                fontName='Helvetica-Bold'
            )

            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=10,
                spaceAfter=6,
                fontName='Helvetica'
            )

            # 标题
            story.append(Paragraph(f"案情分析日报", title_style))
            story.append(Paragraph(f"报告日期: {report_date} | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M')}", normal_style))
            story.append(Spacer(1, 20))

            # 一、案情总体形势
            story.append(Paragraph("一、案情总体形势", heading_style))

            summary_text = f"""
            涉诈案情情况：今日录入案情总数 {case_summary['total_cases']:,} 条，
            涉案总金额 {case_summary['total_amount']:,.2f} 万元。

            涉诈APP情况：今日录入涉诈APP {app_cases['app_count']} 个，
            涉案 {app_cases['case_count']} 起，金额 {app_cases['total_amount']:,.2f} 万元。
            """
            story.append(Paragraph(summary_text, normal_style))

            # 高额案件统计表
            high_amount_data = [
                ['案件类型', '数量'],
                ['50万以上案件', str(case_summary['high_amount_cases_50w'])],
                ['100万以上案件', str(case_summary['high_amount_cases_100w'])],
                ['1000万以上案件', str(case_summary['high_amount_cases_1000w'])]
            ]

            high_amount_table = Table(high_amount_data)
            high_amount_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(high_amount_table)
            story.append(Spacer(1, 20))

            # 二、案情分布
            story.append(Paragraph("二、案情分布", heading_style))

            case_dist_data = [
                ['案件类型', '数量', '说明'],
                ['URL类涉案', str(case_dist['url_cases']), '通过网址链接实施的诈骗'],
                ['APP类涉案', str(case_dist['app_cases']), '通过手机APP实施的诈骗'],
                ['电话/短信类涉案', str(case_dist['call_sms_cases']), '通过电话或短信实施的诈骗'],
                ['国际号码涉案', str(case_dist['intl_number_cases']), '来自国际号码的诈骗']
            ]

            case_dist_table = Table(case_dist_data)
            case_dist_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(case_dist_table)
            story.append(Spacer(1, 15))

            # 运营商分布
            operator_text = f"""
            运营商涉案情况：共 {case_dist['operator_cases']} 个涉案手机号码，
            其中电信号码 {case_dist['telecom_cases']} 个，占比 {case_dist['telecom_percentage']:.2f}%。
            """
            story.append(Paragraph(operator_text, normal_style))

            operator_data = [
                ['运营商', '涉案号码数量'],
                ['中国电信', str(operator_dist['telecom_cases'])],
                ['中国联通', str(operator_dist['unicom_cases'])],
                ['中国移动', str(operator_dist['mobile_cases'])],
                ['中国广电', str(operator_dist['broadcast_cases'])],
                ['虚拟运营商', str(operator_dist['virtual_cases'])]
            ]

            operator_table = Table(operator_data)
            operator_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(operator_table)
            story.append(Spacer(1, 20))

            # 三、重大案件监控
            story.append(Paragraph("三、重大案件监控", heading_style))

            major_text = f"""
            高额案件统计：50万元以上案件共 {major_cases['high_amount_cases']} 起，
            其中运营商相关 {major_cases['operator_related_cases']} 起。
            """
            story.append(Paragraph(major_text, normal_style))

            major_data = [
                ['案件类型', '50万以上', '100万以上', '1000万以上', '涉案金额(万元)'],
                ['中国电信相关',
                 str(major_cases['telecom_cases_50w']),
                 str(major_cases['telecom_cases_100w']),
                 str(major_cases['telecom_cases_1000w']),
                 f"{major_cases['telecom_amount']:,.2f}"]
            ]

            major_table = Table(major_data)
            major_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(major_table)
            story.append(Spacer(1, 30))

            # 页脚
            footer_text = f"数据来源：反诈平台数据库 | 统计周期：{report_date} | 本报告仅供内部使用，请妥善保管"
            story.append(Paragraph(footer_text, normal_style))

            # 构建PDF
            doc.build(story)

            return report_path

        except ImportError:
            print("错误：未安装reportlab库，请运行: pip install reportlab")
            return None
        except Exception as e:
            print(f"生成PDF报告失败: {str(e)}")
            return None

    def generate_enhanced_report(self, report_date, output_dir='/Users/<USER>/Documents/测试'):
        """生成增强版PDF报告"""
        try:
            os.makedirs(output_dir, exist_ok=True)

            # 获取数据
            case_summary = self.analyzer.get_case_summary(report_date)
            app_cases = self.analyzer.get_app_cases(report_date)
            case_dist = self.analyzer.get_case_distribution(report_date)
            operator_dist = self.analyzer.get_operator_distribution(report_date)
            major_cases = self.analyzer.get_major_cases(report_date)
            top_apps = self.analyzer.get_top_apps(report_date, 10)

            # 生成增强版PDF报告
            return self._generate_enhanced_pdf_report(report_date, case_summary, app_cases,
                                                    case_dist, operator_dist, major_cases,
                                                    top_apps, output_dir)

        except Exception as e:
            print(f"生成增强版报告失败: {str(e)}")
            return None

    def _generate_enhanced_pdf_report(self, report_date, case_summary, app_cases, case_dist,
                                    operator_dist, major_cases, top_apps, output_dir):
        """生成增强版PDF报告"""
        try:
            # 导入reportlab库
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib import colors
            from reportlab.lib.enums import TA_CENTER

            # 设置PDF文件路径
            report_path = os.path.join(output_dir, f'enhanced_case_report_{report_date}.pdf')

            # 创建PDF文档
            doc = SimpleDocTemplate(report_path, pagesize=A4)
            story = []

            # 获取样式
            styles = getSampleStyleSheet()

            # 定义样式
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=20,
                spaceAfter=30,
                alignment=TA_CENTER,
                fontName='Helvetica-Bold'
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                fontName='Helvetica-Bold'
            )

            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=10,
                spaceAfter=6,
                fontName='Helvetica'
            )

            # 标题
            story.append(Paragraph(f"案情分析日报（增强版）", title_style))
            story.append(Paragraph(f"报告日期: {report_date} | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M')}", normal_style))
            story.append(Spacer(1, 20))

            # 一、案情总体形势
            story.append(Paragraph("一、案情总体形势", heading_style))

            summary_text = f"""
            今日重点数据：
            • 案情总数：{case_summary['total_cases']:,} 条
            • 涉案金额：{case_summary['total_amount']:,.2f} 万元
            • 涉诈APP：{app_cases['app_count']} 个

            风险提示：本日录入案情 {case_summary['total_cases']:,} 条，
            其中高额案件（50万以上）共 {case_summary['high_amount_cases_50w']} 起，需重点关注。
            """
            story.append(Paragraph(summary_text, normal_style))

            # 统计卡片数据表
            stats_data = [
                ['统计项目', '数值'],
                ['50万以上案件', str(case_summary['high_amount_cases_50w'])],
                ['100万以上案件', str(case_summary['high_amount_cases_100w'])],
                ['1000万以上案件', str(case_summary['high_amount_cases_1000w'])],
                ['APP涉案数', str(app_cases['case_count'])]
            ]

            stats_table = Table(stats_data)
            stats_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(stats_table)
            story.append(Spacer(1, 20))

            # 二、案情分布
            story.append(Paragraph("二、案情分布", heading_style))

            dist_text = f"""
            涉案手段分布：
            • URL类：{case_dist.get('url_cases', 0)} 起
            • APP类：{case_dist.get('app_cases', 0)} 起
            • 电话/短信类：{case_dist.get('call_sms_cases', 0)} 起
            • 国际号码：{case_dist.get('intl_number_cases', 0)} 起

            运营商涉案情况：
            运营商涉案手机记录 {case_dist['operator_cases']} 个，
            其中电信号码 {case_dist['telecom_cases']} 个，占比 {case_dist['telecom_percentage']:.2f}%
            """
            story.append(Paragraph(dist_text, normal_style))

            # 运营商分布表
            operator_data = [
                ['运营商', '涉案号码数量'],
                ['中国电信', str(operator_dist['telecom_cases'])],
                ['中国联通', str(operator_dist['unicom_cases'])],
                ['中国移动', str(operator_dist['mobile_cases'])],
                ['中国广电', str(operator_dist['broadcast_cases'])],
                ['虚拟运营商', str(operator_dist['virtual_cases'])]
            ]

            operator_table = Table(operator_data)
            operator_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(operator_table)
            story.append(Spacer(1, 20))

            # 三、重大案件监控
            story.append(Paragraph("三、重大案件监控", heading_style))

            major_text = f"""
            高额案件统计：
            • 50万以上案件总数：{major_cases['high_amount_cases']} 起
            • 运营商相关案件：{major_cases['operator_related_cases']} 起
            • 电信涉案金额：{major_cases['telecom_amount']:,.2f} 万元
            • 电信50万以上案件：{major_cases['telecom_cases_50w']} 起

            重点关注：中国电信相关案件中，
            50万以上案件 {major_cases['telecom_cases_50w']} 起，
            100万以上案件 {major_cases['telecom_cases_100w']} 起，
            1000万以上案件 {major_cases['telecom_cases_1000w']} 起。
            """
            story.append(Paragraph(major_text, normal_style))

            # 四、涉诈APP分析
            story.append(Paragraph("四、涉诈APP分析", heading_style))

            if not top_apps.empty:
                # TOP涉诈APP表格
                app_data = [['排名', 'APP名称', '案件数', '占比', '涉案金额(万元)', '风险等级']]

                for i, (_, row) in enumerate(top_apps.head(10).iterrows(), 1):
                    risk_level = "高风险" if row['case_count'] > 50 else "中风险" if row['case_count'] > 20 else "低风险"
                    app_data.append([
                        str(i),
                        str(row['app_name']),
                        str(row['case_count']),
                        f"{row['percentage']:.2f}%",
                        f"{row['amount']:,.2f}",
                        risk_level
                    ])

                app_table = Table(app_data)
                app_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))

                story.append(app_table)
            else:
                story.append(Paragraph("暂无涉诈APP数据", normal_style))

            story.append(Spacer(1, 20))

            # 五、分析总结与建议
            story.append(Paragraph("五、分析总结与建议", heading_style))

            summary_analysis = f"""
            今日数据总结：
            • 案情总数 {case_summary['total_cases']:,} 条，涉案金额 {case_summary['total_amount']:,.2f} 万元
            • 涉诈APP共 {app_cases['app_count']} 个，涉案 {app_cases['case_count']} 起
            • 高额案件（50万以上）共 {case_summary['high_amount_cases_50w']} 起，占比 {(case_summary['high_amount_cases_50w'] / max(case_summary['total_cases'], 1) * 100):.2f}%
            • 电信号码涉案比例达 {case_dist['telecom_percentage']:.2f}%

            风险预警：
            • 高额案件数量较多，需加强监控和预警机制
            • 电信号码涉案比例偏高，建议加强电信渠道管控
            • 新增APP类型多样，需持续关注新兴诈骗手段

            工作建议：
            • 加强监控：重点关注高额案件和新增APP的发展趋势
            • 协同治理：加强与运营商的协作，特别是电信号码的管控
            • 技术升级：完善反诈技术手段，提高识别和拦截效率
            • 宣传教育：针对高发案件类型加强公众防范意识宣传
            """
            story.append(Paragraph(summary_analysis, normal_style))

            story.append(Spacer(1, 30))

            # 页脚
            footer_text = f"报告日期：{report_date} | 生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M')} | 数据来源：反诈平台数据库 | 内部机密，请勿外传"
            story.append(Paragraph(footer_text, normal_style))

            # 构建PDF
            doc.build(story)

            return report_path

        except ImportError:
            print("错误：未安装reportlab库，请运行: pip install reportlab")
            return None
        except Exception as e:
            print(f"生成增强版PDF报告失败: {str(e)}")
            return None


class ReportSystem:
    """报告生成系统主类"""

    def __init__(self):
        self.db = DatabaseManager()
        self.analyzer = CaseDataAnalyzer(self.db)
        self.generator = ReportGenerator(self.analyzer)

    def generate_reports(self, report_date=None):
        """生成所有报告"""
        if report_date is None:
            report_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')

        print(f"📅 生成报告日期: {report_date}")
        print("开始生成案情分析报告...")

        results = {}

        # 生成简化版报告
        try:
            simple_report = self.generator.generate_simple_report(report_date)
            results['simple_report'] = simple_report
            print(f"✅ 简化版报告生成成功: {simple_report}")
        except Exception as e:
            print(f"❌ 简化版报告生成失败: {str(e)}")
            results['simple_report'] = None

        # 生成增强版报告
        try:
            enhanced_report = self.generator.generate_enhanced_report(report_date)
            results['enhanced_report'] = enhanced_report
            if enhanced_report:
                print(f"✅ 增强版报告生成成功: {enhanced_report}")
            else:
                print("❌ 增强版报告生成失败")
        except Exception as e:
            print(f"❌ 增强版报告生成失败: {str(e)}")
            results['enhanced_report'] = None

        print("\n==================================================")
        print("📊 报告生成完成!")
        print("==================================================")
        print(f"✅ simple_report: {results.get('simple_report', '无')}")
        print(f"✅ enhanced_report: {results.get('enhanced_report', '无')}")
        print("\n💡 提示: 请在PDF阅读器中打开文件查看报告")

        return results


def main():
    """主函数"""
    try:
        # 检查参数
        if len(sys.argv) < 2:
            report_date = datetime.now().strftime('%Y-%m-%d')  # 使用当天日期
            report_type = 'simple'
        else:
            report_date = sys.argv[1]
            report_type = sys.argv[2] if len(sys.argv) > 2 else 'simple'

        # 创建报告系统并生成报告
        system = ReportSystem()

        if report_type == 'enhanced':
            result = system.generator.generate_enhanced_report(report_date)
            if result:
                print(f"✅ 增强版PDF报告生成成功: {result}")
            else:
                print("❌ 增强版PDF报告生成失败")
        else:
            result = system.generator.generate_simple_report(report_date)
            if result:
                print(f"✅ 简化版PDF报告生成成功: {result}")
            else:
                print("❌ 简化版PDF报告生成失败")

        print("报告生成完成！")

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
