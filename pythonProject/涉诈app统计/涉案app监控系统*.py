import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime, timedelta
import warnings
import pymysql
import json
import os
from pathlib import Path
from tqdm import tqdm
import seaborn as sns
import re
from collections import defaultdict, Counter
import jieba
from wordcloud import WordCloud
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import networkx as nx
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
import hashlib
from sqlalchemy import create_engine
from urllib.parse import quote_plus
from sqlalchemy import create_engine, text
from urllib.parse import quote_plus
import threading
from concurrent.futures import ThreadPoolExecutor
import time

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False


class AdvancedAppFraudMonitor:
    """升级版APP涉案监控系统"""

    def __init__(self):
        self.connection = None
        self.is_connected = False

        # 🔥 重要：先定义数据库配置，再初始化引擎
        self.db_config = {
            'host': '************',
            'port': 57861,
            'user': 'FzUser',
            'password': 'jZ4%fQ3}oI8(jH7)',
            'database': 'antiFraudPlatform',
            'charset': 'utf8mb4'
        }

        # 使用连接池
        self.engine = None
        self._memory_cache = {}
        self._cache_lock = threading.Lock()

        # 配色方案
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'accent': '#F18F01',
            'danger': '#C73E1D',
            'warning': '#F0BE38',
            'success': '#5C946E',
            'neutral': '#8B9DC3',
            'background': '#F8F9FA',
            'grid': '#E9ECEF',
            'text': '#2C3E50'
        }

        # 缓存配置
        self.cache_dir = Path("../cache")
        self.cache_dir.mkdir(exist_ok=True)
        self.cache_file = self.cache_dir / "advanced_fraud_cache.json"

        # 图表导出配置
        self.export_dir = Path("/Users/<USER>/Documents/测试")
        self.export_dir.mkdir(exist_ok=True)

        # 颜色调色板
        self.color_palette = [
            '#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#5C946E',
            '#8B9DC3', '#F0BE38', '#7B68EE', '#20B2AA', '#FF6347',
            '#32CD32', '#FFD700', '#FF69B4', '#1E90FF', '#FF4500',
            '#9370DB', '#00CED1', '#FFA500', '#DC143C', '#00FA9A',
            '#4169E1', '#FF1493', '#00BFFF', '#FF6B6B', '#4ECDC4',
            '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'
        ]

        # 数据库字段映射
        self.field_mapping = {
            'case_number': '案件编号',
            'case_category': '案件类别',
            # ... 保留其他字段映射 ...
            'insert_day': '入库时间'
        }

        # 预警阈值配置
        self.alert_thresholds = {
            'new_app_cases': 5,
            'amount_spike': 2.0,
            'case_spike': 3.0,
            'high_risk_amount': 100000,
        }

        # 🔥 最后初始化数据库引擎
        self._init_database_engine()

    def _init_database_engine(self):
        """初始化SQLAlchemy数据库引擎"""
        try:
            # URL编码密码中的特殊字符
            password_encoded = quote_plus(self.db_config['password'])

            connection_string = (
                f"mysql+pymysql://{self.db_config['user']}:{password_encoded}@"
                f"{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}"
                f"?charset={self.db_config['charset']}"
            )

            # 连接池配置
            self.engine = create_engine(
                connection_string,
                pool_size=10,  # 连接池大小
                max_overflow=20,  # 最大溢出连接数
                pool_timeout=30,  # 获取连接超时时间
                pool_recycle=3600,  # 连接回收时间(1小时)
                pool_pre_ping=True,  # 连接前ping测试
                echo=False  # 不显示SQL日志
            )

            # 测试连接
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            print("✅ 数据库引擎初始化成功")
            self.is_connected = True

        except Exception as e:
            print(f"❌ 数据库引擎初始化失败: {e}")
            self.engine = None
            self.is_connected = False

    def connect_database(self):
        """连接数据库（使用连接池）"""
        if self.engine is None:
            self._init_database_engine()

        # 🔥 添加返回值检查
        return self.is_connected

    def get_comprehensive_data(self, days=30, use_cache=True):
        """获取全面的案件数据（优化版）"""
        cache_key = f"comprehensive_data_{days}days"

        # 1. 检查内存缓存
        if use_cache:
            cached_data = self._get_memory_cache(cache_key)
            if cached_data is not None:
                print("🚀 使用内存缓存")
                return cached_data

        # 2. 检查文件缓存
        if use_cache and self.cache_file.exists():
            cached_data = self._load_file_cache()
            if cached_data is not None:
                print("📦 使用文件缓存")
                self._set_memory_cache(cache_key, cached_data)
                return cached_data

        # 3. 从数据库查询
        print("🔄 从数据库获取数据...")
        df = self._query_comprehensive_database_optimized(days)

        # 4. 保存缓存
        if use_cache and df is not None and not df.empty:
            self._set_memory_cache(cache_key, df)
            self._save_file_cache_async(df)

        return df

    def _save_file_cache_async(self, df):
        """异步保存文件缓存"""

        def save_cache():
            try:
                cache_data = {
                    'cache_date': datetime.now().strftime('%Y-%m-%d'),
                    'cache_time': datetime.now().isoformat(),
                    'record_count': len(df),
                    'data': df.to_dict('records')
                }

                with open(self.cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, ensure_ascii=False, indent=2, default=str)

                print("💾 缓存已异步保存")
            except Exception as e:
                print(f"⚠️ 缓存保存失败: {e}")

        # 使用线程异步保存
        thread = threading.Thread(target=save_cache)
        thread.daemon = True
        thread.start()
    def _load_file_cache(self):
        """加载文件缓存"""
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)

            cache_date = cache_data.get('cache_date')
            today = datetime.now().strftime('%Y-%m-%d')

            if cache_date == today:
                df = pd.DataFrame(cache_data['data'])

                # 快速类型转换
                if 'insert_day' in df.columns:
                    df['insert_day'] = pd.to_datetime(df['insert_day'])
                if 'occurrence_time' in df.columns:
                    df['occurrence_time'] = pd.to_datetime(df['occurrence_time'])
                if 'involved_amount' in df.columns:
                    df['involved_amount'] = pd.to_numeric(df['involved_amount'], errors='coerce')
                if 'victim_age' in df.columns:
                    df['victim_age'] = pd.to_numeric(df['victim_age'], errors='coerce')

                return df
        except Exception as e:
            print(f"⚠️ 文件缓存加载失败: {e}")

        return None
    def _set_memory_cache(self, key, df):
        """设置内存缓存"""
        with self._cache_lock:
            self._memory_cache[key] = (datetime.now(), df.copy())
            # 限制缓存数量
            if len(self._memory_cache) > 5:
                oldest_key = min(self._memory_cache.keys(),
                                 key=lambda k: self._memory_cache[k][0])
                del self._memory_cache[oldest_key]

    def _query_comprehensive_database_optimized(self, days=30):
        """优化的数据库查询"""
        if not self.connect_database():
            return None

        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

            # 分阶段查询策略
            print(f"🔍 执行优化查询 ({start_date} 至 {end_date})...")

            # 第一阶段：获取核心数据
            core_query = text("""
                SELECT 
                    case_number, 
                    app_name as final_app_name,
                    involved_amount, 
                    insert_day, 
                    occurrence_area,
                    case_main_type, 
                    case_sub_type, 
                    victim_age,
                    brief_case_description
                FROM anti_fraud_case_new
                WHERE DATE(insert_day) BETWEEN :start_date AND :end_date
                    AND app_name IS NOT NULL
                    AND app_name != ''
                    AND involved_amount > 0
                ORDER BY insert_day DESC
            """)

            start_time = time.time()

            with self.engine.connect() as conn:
                result = conn.execute(core_query, {
                    'start_date': start_date,
                    'end_date': end_date
                })

                # 批量获取数据
                rows = result.fetchall()
                columns = result.keys()

                # 转换为DataFrame
                df = pd.DataFrame(rows, columns=columns)

            query_time = time.time() - start_time
            print(f"⚡ 查询完成，耗时 {query_time:.2f}秒，获取 {len(df)} 条记录")

            if not df.empty:
                # 快速数据类型转换
                df = self._fast_data_conversion(df)

                # 数据清理
                df = self.clean_and_filter_data(df)

                print(f"✅ 数据处理完成")
                print(f"📊 有效APP数量: {df['final_app_name'].dropna().nunique()} 个")
                print(f"💰 总涉案金额: {df['involved_amount'].sum():,.2f} 元")

            return df

        except Exception as e:
            print(f"❌ 优化查询失败: {e}")
            return None

    def _get_memory_cache(self, key):
        """获取内存缓存"""
        with self._cache_lock:
            if key in self._memory_cache:
                cache_time, cached_df = self._memory_cache[key]
                # 缓存有效期1小时
                if (datetime.now() - cache_time).seconds < 3600:
                    return cached_df.copy()
                else:
                    del self._memory_cache[key]
        return None
    def _fast_data_conversion(self, df):
        """快速数据类型转换"""
        try:
            # 日期转换
            date_columns = ['insert_day', 'occurrence_time']
            for col in date_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], errors='coerce')

            # 数值转换
            numeric_columns = ['involved_amount', 'victim_age']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            return df
        except Exception as e:
            print(f"⚠️ 数据转换警告: {e}")
            return df

    def query_with_pagination(self, days=30, page_size=10000):
        """分页查询大数据集"""
        if not self.connect_database():
            return None

        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

            all_data = []
            offset = 0

            print(f"📄 开始分页查询...")

            while True:
                query = text("""
                    SELECT 
                        case_number, case_main_type, case_sub_type,
                        occurrence_area, involved_amount,
                        COALESCE(app_name, service_account_app_name) as final_app_name,
                        victim_age, insert_day
                    FROM anti_fraud_case_new
                    WHERE DATE(insert_day) BETWEEN :start_date AND :end_date
                        AND (app_name IS NOT NULL OR service_account_app_name IS NOT NULL)
                    ORDER BY insert_day DESC
                    LIMIT :page_size OFFSET :offset
                """)

                with self.engine.connect() as conn:
                    result = conn.execute(query, {
                        'start_date': start_date,
                        'end_date': end_date,
                        'page_size': page_size,
                        'offset': offset
                    })

                    rows = result.fetchall()
                    if not rows:
                        break

                    columns = result.keys()
                    df_page = pd.DataFrame(rows, columns=columns)
                    all_data.append(df_page)

                    offset += page_size
                    print(f"📄 已获取 {offset} 条记录...")

                    if len(rows) < page_size:
                        break

            if all_data:
                final_df = pd.concat(all_data, ignore_index=True)
                final_df = self._fast_data_conversion(final_df)
                print(f"✅ 分页查询完成，总计 {len(final_df)} 条记录")
                return final_df
            else:
                print("⚠️ 分页查询无结果")
                return pd.DataFrame()

        except Exception as e:
            print(f"❌ 分页查询失败: {e}")
            return None

    def _query_comprehensive_database(self, days=30):
        """优化查询策略"""
        if not self.connect_database():
            return None

        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

            # 添加查询优化
            optimized_query = f"""
            SELECT 
                case_number, 
                app_name as final_app_name,
                involved_amount, 
                insert_day, 
                occurrence_area,
                case_main_type, 
                case_sub_type, 
                victim_age,
                brief_case_description
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
                AND app_name IS NOT NULL
                AND app_name != ''
                AND involved_amount > 0
            ORDER BY insert_day DESC
            """

            print(f"🔍 执行优化查询 ({start_date} 至 {end_date})...")

            # 使用引擎执行查询
            df = pd.read_sql(optimized_query, self.engine)

            if not df.empty:
                # 快速数据处理
                df['insert_day'] = pd.to_datetime(df['insert_day'])
                df['involved_amount'] = pd.to_numeric(df['involved_amount'], errors='coerce')
                df['victim_age'] = pd.to_numeric(df['victim_age'], errors='coerce')

                print(f"✅ 查询完成，获取 {len(df)} 条记录")

            return df

        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return None

    def _process_app_names(self, df):
        """简化APP名称处理，只使用app_name字段"""
        if df is None or df.empty:
            return df

        # 直接使用app_name作为final_app_name
        if 'app_name' in df.columns:
            df['final_app_name'] = df['app_name']
        else:
            df['final_app_name'] = '未知'

        return df

    def fetch_app_fraud_data(self, days=30, use_cache=True):
        """获取APP涉诈数据 - 简化版"""
        cache_key = f"app_fraud_data_{days}"

        # 检查缓存
        if use_cache:
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                print(f"📋 使用缓存数据 (过去{days}天)")
                return cached_data

        print(f"🔍 查询过去{days}天的APP涉诈数据...")

        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

            # 简化查询，只使用app_name
            query = f"""
            SELECT 
                case_number,
                app_name as final_app_name,
                involved_amount,
                insert_day,
                occurrence_area,
                case_main_type,
                case_sub_type,
                victim_age,
                brief_case_description
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
                AND app_name IS NOT NULL
                AND app_name != ''
                AND involved_amount > 0
            ORDER BY insert_day DESC
            """

            # 执行查询
            with self.engine.connect() as conn:
                df = pd.read_sql(query, conn)

            if df.empty:
                print("⚠️ 未查询到数据")
                return None

            print(f"✅ 查询成功，获取 {len(df)} 条记录")

            # 缓存结果
            if use_cache:
                self._store_in_cache(cache_key, df)

            return df

        except Exception as e:
            print(f"❌ 数据查询失败: {e}")
            return None

    def clean_and_filter_data(self, df):
        """清理和过滤数据"""
        if df is None or df.empty:
            return df

        print("🧹 清理数据...")
        original_count = len(df)

        # 1. 处理APP名称拆分
        if 'final_app_name' in df.columns:
            # 拆分包含逗号的APP名称
            expanded_rows = []
            for _, row in df.iterrows():
                app_name = str(row['final_app_name'])
                if ',' in app_name:
                    # 拆分APP名称
                    app_names = [name.strip() for name in app_name.split(',') if name.strip()]
                    for single_app in app_names:
                        new_row = row.copy()
                        new_row['final_app_name'] = single_app
                        expanded_rows.append(new_row)
                else:
                    expanded_rows.append(row)

            df = pd.DataFrame(expanded_rows)
            print(f"   APP名称拆分后: {len(df)} 条记录")

        # 2. 过滤无效数据
        if 'involved_amount' in df.columns:
            df = df[df['involved_amount'] > 0]

        # 3. 过滤无效APP名称
        if 'final_app_name' in df.columns:
            invalid_patterns = ['未知', '无', 'null', 'NULL', '', '其他', '不详', '不明']
            df = df[~df['final_app_name'].isin(invalid_patterns)]
            df = df[df['final_app_name'].str.len() > 1]  # 至少2个字符

        # 4. 数据类型转换
        if 'insert_day' in df.columns:
            df['insert_day'] = pd.to_datetime(df['insert_day'])

        cleaned_count = len(df)
        print(f"   清理完成: {original_count} → {cleaned_count} 条记录")

        return df

    def filter_invalid_app_names(self, df, app_column='final_app_name'):
        """过滤无意义的APP名称"""
        if df is None or df.empty or app_column not in df.columns:
            return df

        original_apps = df[app_column].dropna().nunique()
        print(f"🔍 过滤前APP数量: {original_apps}")

        # 创建清理副本
        df_work = df.copy()
        df_work[f'{app_column}_clean'] = df_work[app_column].astype(str).str.strip()

        # 过滤条件
        mask = pd.Series(True, index=df_work.index)

        # 1. 过滤空值和空字符串
        mask &= df_work[f'{app_column}_clean'].notna()
        mask &= df_work[f'{app_column}_clean'] != ''
        mask &= df_work[f'{app_column}_clean'] != 'nan'

        # 2. 过滤纯数字
        mask &= ~df_work[f'{app_column}_clean'].str.match(r'^\d+$', na=False)

        # 3. 过滤特殊符号模式
        special_patterns = [r'^[?？]+$', r'^\*+$', r'^#+$', r'^[\.。]+$', r'^[-—]+$', r'^[/\\]+$']
        for pattern in special_patterns:
            mask &= ~df_work[f'{app_column}_clean'].str.match(pattern, na=False)

        # 4. 过滤无意义词汇
        meaningless_patterns = [
            r'^无$', r'^wu$', r'^未知$', r'^unknown$', r'^null$',
            r'^其他$', r'^其它$', r'^不详$', r'^不明$', r'^暂无$', r'^待定$',
            r'^test$', r'^demo$', r'^temp$', r'^临时$', r'^测试$',
            r'^韩文$', r'^日文$', r'^符号$', r'^特殊符号$', r'^已删除$',
            r'^受害人无法提供$', r'^teams?$', r'^app$', r'^APP$'
        ]
        for pattern in meaningless_patterns:
            mask &= ~df_work[f'{app_column}_clean'].str.match(pattern, case=False, na=False)

        # 应用过滤
        filtered_df = df_work[mask].copy()
        filtered_df = filtered_df.drop(f'{app_column}_clean', axis=1)

        filtered_apps = filtered_df[app_column].dropna().nunique()
        print(f"✅ 过滤后APP数量: {filtered_apps}")

        return filtered_df

    def analyze_financial_impact(self, df):
        """分析资金影响（优化损失等级）"""
        if df is None or df.empty:
            print("❌ 无法分析资金影响：无数据")
            return None

        print("\n💰 资金影响分析")
        print("=" * 60)

        if 'involved_amount' not in df.columns:
            print("⚠️ 数据中缺少涉案金额字段")
            return None

        # 过滤有效金额数据
        amount_data = df['involved_amount'].dropna()
        amount_data = amount_data[amount_data > 0]

        if amount_data.empty:
            print("⚠️ 无有效金额数据")
            return None

        # 基础统计
        total_amount = amount_data.sum()
        avg_amount = amount_data.mean()
        median_amount = amount_data.median()
        max_amount = amount_data.max()
        case_count = len(amount_data)

        print(f"📊 涉案总金额: {total_amount:,.2f} 元")
        print(f"📊 平均损失: {avg_amount:,.2f} 元")
        print(f"📊 中位数损失: {median_amount:,.2f} 元")
        print(f"📊 最高单笔: {max_amount:,.2f} 元")
        print(f"📊 有效案件数: {case_count} 起")

        # 优化的损失等级分析
        conditions = [
            amount_data < 10000,
            (amount_data >= 10000) & (amount_data < 100000),
            (amount_data >= 100000) & (amount_data < 500000),
            (amount_data >= 500000) & (amount_data < 1000000),
            amount_data >= 1000000
        ]
        choices = ['小额(<1万)', '中额(1-10万)', '大额(10-50万)', '巨大额(50-100万)', '巨巨大额(≥100万)']
        df_with_amount = df.dropna(subset=['involved_amount']).copy()
        df_with_amount['amount_level'] = np.select(conditions, choices, default='未知')

        level_stats = df_with_amount.groupby('amount_level').agg({
            'involved_amount': ['count', 'sum', 'mean']
        }).round(2)

        print(f"\n💰 损失等级分布:")
        for level in ['小额(<1万)', '中额(1-10万)', '大额(10-50万)', '巨大额(50-100万)', '巨巨大额(≥100万)']:
            if level in level_stats.index:
                count = level_stats.loc[level, ('involved_amount', 'count')]
                total = level_stats.loc[level, ('involved_amount', 'sum')]
                avg = level_stats.loc[level, ('involved_amount', 'mean')]
                percentage = (count / case_count) * 100
                print(f"  {level}: {count}起 ({percentage:.1f}%) 总额:{total:,.0f}元 均额:{avg:,.0f}元")

        # APP维度分析
        if 'final_app_name' in df.columns:
            app_amount_stats = df_with_amount.groupby('final_app_name').agg({
                'involved_amount': ['count', 'sum', 'mean', 'max']
            }).round(2)
            app_amount_stats.columns = ['案件数', '总金额', '平均金额', '最高金额']
            app_amount_stats = app_amount_stats.sort_values('总金额', ascending=False)

            print(f"\n🏆 TOP10 高损失APP:")
            print("-" * 60)
            for i, (app, row) in enumerate(app_amount_stats.head(10).iterrows(), 1):
                print(f"{i:2d}. {app}")
                print(f"    案件: {row['案件数']}起 | 总额: {row['总金额']:,.0f}元 | 均额: {row['平均金额']:,.0f}元")

        return df_with_amount

    def detect_new_apps(self, df, days_threshold=7, display_limit=50):
        """检测新出现的APP（按均金额排序，去掉风险等级）"""
        if df is None or df.empty or 'final_app_name' not in df.columns:
            print("❌ 无法检测新APP：缺少APP数据")
            return None

        print(f"\n🆕 新APP检测 (最近{days_threshold}天)")
        print("=" * 60)

        # 计算每个APP首次出现的时间
        app_first_seen = df.dropna(subset=['final_app_name']).groupby('final_app_name')['insert_day'].min()

        # 检测最近出现的APP
        threshold_date = datetime.now() - timedelta(days=days_threshold)
        new_apps = app_first_seen[app_first_seen >= threshold_date]

        if new_apps.empty:
            print(f"✅ 最近{days_threshold}天未发现新APP")
            return None

        total_new_apps = len(new_apps)
        print(f"🔍 发现 {total_new_apps} 个新APP:")

        # 分析新APP的案件情况
        new_app_analysis = []
        for app_name, first_seen in new_apps.items():
            app_cases = df[df['final_app_name'] == app_name]
            case_count = len(app_cases)
            total_amount = app_cases['involved_amount'].sum() if 'involved_amount' in app_cases.columns else 0
            avg_amount = app_cases['involved_amount'].mean() if 'involved_amount' in app_cases.columns else 0

            new_app_analysis.append({
                'app_name': app_name,
                'first_seen': first_seen,
                'case_count': case_count,
                'total_amount': total_amount,
                'avg_amount': avg_amount if not pd.isna(avg_amount) else 0
            })

        # 按均金额排序（从高到低）
        new_app_analysis.sort(key=lambda x: x['avg_amount'], reverse=True)

        # 限制显示数量
        display_apps = new_app_analysis[:display_limit]

        if total_new_apps > display_limit:
            print(f"📋 显示TOP{display_limit}个新APP (按均金额排序，共{total_new_apps}个):")
        else:
            print(f"📋 显示全部{total_new_apps}个新APP (按均金额排序):")

        # 显示结果（去掉风险等级）
        for i, app_info in enumerate(display_apps, 1):
            print(f"{i:2d}. {app_info['app_name']}")
            print(f"    首次发现: {app_info['first_seen'].strftime('%Y-%m-%d')}")
            print(f"    案件数: {app_info['case_count']}起")
            if app_info['total_amount'] > 0:
                print(f"    涉案金额: {app_info['total_amount']:,.0f}元 (均:{app_info['avg_amount']:,.0f}元)")

        # 如果有未显示的APP，提供统计信息
        if total_new_apps > display_limit:
            remaining_apps = new_app_analysis[display_limit:]
            remaining_total_cases = sum(app['case_count'] for app in remaining_apps)
            remaining_total_amount = sum(app['total_amount'] for app in remaining_apps)
            print(f"\n📊 未显示的{total_new_apps - display_limit}个APP统计:")
            print(f"   总案件数: {remaining_total_cases}起 | 总涉案金额: {remaining_total_amount:,.0f}元")

        return pd.DataFrame(new_app_analysis)

    def assess_app_risk(self, case_count, total_amount, days):
        """评估APP风险等级"""
        daily_cases = case_count / max(days, 1)
        daily_amount = total_amount / max(days, 1)

        # 风险评级规则
        if daily_cases >= 5 or daily_amount >= 50000:
            return '高'
        elif daily_cases >= 2 or daily_amount >= 10000:
            return '中'
        else:
            return '低'

    def analyze_fraud_methods(self, df):
        """分析诈骗手段分布（优化转账方式处理）"""
        if df is None or df.empty:
            print("❌ 无法分析诈骗手段：无数据")
            return None

        print("\n🎭 诈骗手段分析")
        print("=" * 60)

        analysis_results = {}

        # 1. 案件大类分析
        if 'case_main_type' in df.columns:
            main_type_dist = df['case_main_type'].value_counts()
            print("📊 案件大类分布:")
            for main_type, count in main_type_dist.head(10).items():
                percentage = (count / len(df)) * 100
                print(f"  {main_type}: {count}起 ({percentage:.1f}%)")
            analysis_results['main_types'] = main_type_dist

        # 2. 案件子类分析
        if 'case_sub_type' in df.columns:
            sub_type_dist = df['case_sub_type'].value_counts()
            print(f"\n📊 案件子类分布 (TOP10):")
            for sub_type, count in sub_type_dist.head(10).items():
                percentage = (count / len(df)) * 100
                print(f"  {sub_type}: {count}起 ({percentage:.1f}%)")
            analysis_results['sub_types'] = sub_type_dist

        # 3. 转账方式分析（优化处理）
        if 'transfer_method' in df.columns:
            transfer_data = df['transfer_method'].dropna().astype(str)

            # 标准化转账方式
            def standardize_transfer_method(method):
                method = str(method).strip()
                # 处理重复的转账方式
                if '网银转账' in method:
                    return '网银转账'
                elif '其他方式转账' in method:
                    return '其他方式转账'
                elif '支付宝' in method:
                    return '支付宝转账'
                elif '微信' in method:
                    return '微信转账'
                elif '银行卡' in method:
                    return '银行卡转账'
                elif '现金' in method:
                    return '现金支付'
                elif 'ATM' in method or 'atm' in method.lower():
                    return 'ATM转账'
                elif '手机银行' in method:
                    return '手机银行转账'
                else:
                    return method

            transfer_data_clean = transfer_data.apply(standardize_transfer_method)
            transfer_dist = transfer_data_clean.value_counts()

            print(f"\n💳 转账方式分布:")
            for method, count in transfer_dist.head(10).items():
                percentage = (count / df['transfer_method'].dropna().count()) * 100
                print(f"  {method}: {count}起 ({percentage:.1f}%)")
            analysis_results['transfer_methods'] = transfer_dist

        # 4. 地区分布分析（标准化地区名称）
        if 'occurrence_area' in df.columns:
            area_data = df['occurrence_area'].dropna().astype(str)

            # 标准化地区名称
            def standardize_area_name(area):
                area = str(area).strip()
                # 提取省份信息，统一到省级
                if '省' in area:
                    # 提取省份名称
                    province = area.split('省')[0] + '省'
                    return province
                elif '市' in area and '省' not in area:
                    # 直辖市处理
                    if any(city in area for city in ['北京', '上海', '天津', '重庆']):
                        return area.split('市')[0] + '市'
                    else:
                        return area
                elif '自治区' in area:
                    return area.split('自治区')[0] + '自治区'
                elif '特别行政区' in area:
                    return area.split('特别行政区')[0] + '特别行政区'
                else:
                    return area

            area_data_clean = area_data.apply(standardize_area_name)
            area_dist = area_data_clean.value_counts()

            print(f"\n🗺️ 发案地区分布 (TOP10):")
            for area, count in area_dist.head(10).items():
                percentage = (count / df['occurrence_area'].dropna().count()) * 100
                print(f"  {area}: {count}起 ({percentage:.1f}%)")
            analysis_results['areas'] = area_dist

        return analysis_results

    def analyze_comprehensive_trends(self, df):
        """综合趋势分析（优化地区处理）"""
        if df is None or df.empty:
            print("❌ 无法进行综合分析：无数据")
            return None

        print("\n📈 综合趋势分析")
        print("=" * 60)

        analysis_results = {}

        # 其他分析保持不变...

        # 4. 地区分布分析（优化处理）
        if 'occurrence_area' in df.columns:
            # 标准化地区名称到地级市
            def standardize_area(area):
                if pd.isna(area):
                    return None
                area = str(area).strip()

                # 提取省市信息
                import re
                # 匹配模式：省+市
                pattern = r'([^省]+省)?([^市]+市)'
                match = re.search(pattern, area)

                if match:
                    province = match.group(1) if match.group(1) else ''
                    city = match.group(2) if match.group(2) else ''

                    # 特殊处理直辖市
                    if city in ['北京市', '上海市', '天津市', '重庆市']:
                        return city
                    elif province and city:
                        return f"{province}{city}"
                    elif city:
                        return city
                    else:
                        return area
                else:
                    return area

            area_data = df['occurrence_area'].apply(standardize_area)
            area_dist = area_data.dropna().value_counts()

            print(f"\n🗺️ 发案地区分布 (TOP10):")
            for area, count in area_dist.head(10).items():
                percentage = (count / area_data.dropna().count()) * 100
                print(f"  {area}: {count}起 ({percentage:.1f}%)")
            analysis_results['areas'] = area_dist

        return analysis_results

    def analyze_victim_profile(self, df):
        """分析受害人画像"""
        if df is None or df.empty:
            print("❌ 无法分析受害人画像：无数据")
            return None

        print("\n👤 受害人画像分析")
        print("=" * 60)

        profile_results = {}

        # 年龄分析
        if 'victim_age' in df.columns:
            age_data = df['victim_age'].dropna()
            if not age_data.empty:
                avg_age = age_data.mean()
                median_age = age_data.median()
                min_age = age_data.min()
                max_age = age_data.max()

                print(f"👥 年龄统计:")
                print(f"  平均年龄: {avg_age:.1f}岁")
                print(f"  中位数年龄: {median_age:.1f}岁")
                print(f"  年龄范围: {min_age:.0f}-{max_age:.0f}岁")

                # 年龄段分布（按年龄顺序排列）
                conditions = [
                    age_data < 18,
                    (age_data >= 18) & (age_data < 30),
                    (age_data >= 30) & (age_data < 40),
                    (age_data >= 40) & (age_data < 50),
                    (age_data >= 50) & (age_data < 60),
                    age_data >= 60
                ]
                choices = ['未成年(<18)', '青年(18-30)', '中年(30-40)', '中年(40-50)', '中老年(50-60)', '老年(60+)']
                df_with_age = df.dropna(subset=['victim_age']).copy()
                df_with_age['age_group'] = np.select(conditions, choices, default='未知')

                age_dist = df_with_age['age_group'].value_counts()

                print(f"\n👥 年龄段分布:")
                # 按年龄顺序显示
                age_order = ['未成年(<18)', '青年(18-30)', '中年(30-40)', '中年(40-50)', '中老年(50-60)', '老年(60+)']
                for age_group in age_order:
                    if age_group in age_dist.index:
                        count = age_dist[age_group]
                        percentage = (count / len(age_data)) * 100
                        print(f"  {age_group}: {count}人 ({percentage:.1f}%)")

                profile_results['age_distribution'] = age_dist

            # 与APP和金额的关联分析
            if 'final_app_name' in df.columns and 'victim_age' in df.columns:
                app_age_analysis = df.dropna(subset=['final_app_name', 'victim_age']).groupby('final_app_name')[
                    'victim_age'].agg(['mean', 'count']).round(1)
                app_age_analysis = app_age_analysis[app_age_analysis['count'] >= 5]  # 至少5个案例
                app_age_analysis = app_age_analysis.sort_values('mean')

                print(f"\n📱 不同APP受害人平均年龄 (案件数≥5):")
                for app, row in app_age_analysis.head(10).iterrows():
                    print(f"  {app}: {row['mean']:.1f}岁 ({row['count']}起案件)")

        return profile_results

    def analyze_network_relationships(self, df):
        """分析通联关系和网络模式"""
        if df is None or df.empty:
            print("❌ 无法进行通联关系分析：无数据")
            return None

        print("\n🕸️ 通联关系分析")
        print("=" * 60)

        network_results = {}

        try:
            # 1. 分析共同受害人（基于手机号）
            if 'suspect_phone_number' in df.columns and 'final_app_name' in df.columns:
                phone_app_analysis = self._analyze_phone_app_relationships(df)
                if phone_app_analysis:
                    network_results['phone_app_relationships'] = phone_app_analysis

            # 2. 分析跨APP受害人模式
            if 'victim_app_account' in df.columns and 'final_app_name' in df.columns:
                cross_app_analysis = self._analyze_cross_app_victims(df)
                if cross_app_analysis:
                    network_results['cross_app_victims'] = cross_app_analysis

            # 3. 分析可疑账号集群
            if 'suspect_account_number' in df.columns:
                account_cluster_analysis = self._analyze_account_clusters(df)
                if account_cluster_analysis:
                    network_results['account_clusters'] = account_cluster_analysis

            # 4. 分析时间关联模式
            if 'occurrence_time' in df.columns:
                temporal_analysis = self._analyze_temporal_patterns(df)
                if temporal_analysis:
                    network_results['temporal_patterns'] = temporal_analysis

            print(f"✅ 通联关系分析完成，发现 {len(network_results)} 类关联模式")

        except Exception as e:
            print(f"⚠️ 通联关系分析部分失败: {e}")

        return network_results

    def _analyze_phone_app_relationships(self, df):
        """分析手机号与APP的关联关系"""
        try:
            phone_app_data = df.dropna(subset=['suspect_phone_number', 'final_app_name'])
            if phone_app_data.empty:
                return None

            # 统计每个手机号涉及的APP数量
            phone_app_counts = phone_app_data.groupby('suspect_phone_number')['final_app_name'].nunique()
            multi_app_phones = phone_app_counts[phone_app_counts > 1]

            if not multi_app_phones.empty:
                print(f"📱 发现 {len(multi_app_phones)} 个手机号涉及多个APP:")
                for phone, app_count in multi_app_phones.head(10).items():
                    apps = phone_app_data[phone_app_data['suspect_phone_number'] == phone]['final_app_name'].unique()
                    print(f"  {phone}: {app_count}个APP ({', '.join(apps[:3])}{'...' if len(apps) > 3 else ''})")

                return {
                    'multi_app_phones_count': len(multi_app_phones),
                    'max_apps_per_phone': multi_app_phones.max(),
                    'avg_apps_per_phone': multi_app_phones.mean()
                }
        except Exception as e:
            print(f"⚠️ 手机号APP关联分析失败: {e}")
        return None

    def _analyze_cross_app_victims(self, df):
        """分析跨APP受害人模式"""
        try:
            # 这里可以基于受害人的其他标识符进行分析
            # 由于数据隐私，我们使用案件时间和金额的相似性来推断
            if 'involved_amount' in df.columns and 'occurrence_time' in df.columns:
                # 寻找相似金额和时间的案件
                similar_cases = []
                amount_tolerance = 0.05  # 5%的金额容差
                time_tolerance = timedelta(hours=24)  # 24小时时间容差

                df_clean = df.dropna(subset=['involved_amount', 'occurrence_time', 'final_app_name'])

                for i, row1 in df_clean.iterrows():
                    similar_count = 0
                    for j, row2 in df_clean.iterrows():
                        if i != j and row1['final_app_name'] != row2['final_app_name']:
                            # 检查金额相似性
                            amount_diff = abs(row1['involved_amount'] - row2['involved_amount']) / max(row1['involved_amount'], 1)
                            # 检查时间相似性
                            time_diff = abs(row1['occurrence_time'] - row2['occurrence_time'])

                            if amount_diff <= amount_tolerance and time_diff <= time_tolerance:
                                similar_count += 1

                    if similar_count > 0:
                        similar_cases.append({
                            'app': row1['final_app_name'],
                            'amount': row1['involved_amount'],
                            'time': row1['occurrence_time'],
                            'similar_cases': similar_count
                        })

                if similar_cases:
                    print(f"🔗 发现 {len(similar_cases)} 个可能的跨APP受害模式")
                    return {
                        'potential_cross_app_cases': len(similar_cases),
                        'top_patterns': sorted(similar_cases, key=lambda x: x['similar_cases'], reverse=True)[:5]
                    }
        except Exception as e:
            print(f"⚠️ 跨APP受害人分析失败: {e}")
        return None

    def _analyze_account_clusters(self, df):
        """分析可疑账号集群"""
        try:
            if 'suspect_account_number' in df.columns:
                account_data = df.dropna(subset=['suspect_account_number'])
                if not account_data.empty:
                    # 统计账号出现频次
                    account_counts = account_data['suspect_account_number'].value_counts()
                    frequent_accounts = account_counts[account_counts > 1]

                    if not frequent_accounts.empty:
                        print(f"💳 发现 {len(frequent_accounts)} 个重复出现的可疑账号")
                        return {
                            'frequent_accounts_count': len(frequent_accounts),
                            'max_cases_per_account': frequent_accounts.max(),
                            'top_accounts': frequent_accounts.head(5).to_dict()
                        }
        except Exception as e:
            print(f"⚠️ 账号集群分析失败: {e}")
        return None

    def _analyze_temporal_patterns(self, df):
        """分析时间关联模式"""
        try:
            if 'occurrence_time' in df.columns:
                time_data = df.dropna(subset=['occurrence_time'])
                if not time_data.empty:
                    # 分析案件发生的时间模式
                    time_data['hour'] = time_data['occurrence_time'].dt.hour
                    time_data['day_of_week'] = time_data['occurrence_time'].dt.dayofweek

                    # 高发时段分析
                    hour_counts = time_data['hour'].value_counts().sort_index()
                    peak_hours = hour_counts.nlargest(3)

                    # 高发星期分析
                    weekday_counts = time_data['day_of_week'].value_counts().sort_index()
                    weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

                    print(f"⏰ 案件高发时段: {', '.join([f'{h}时({c}起)' for h, c in peak_hours.items()])}")

                    return {
                        'peak_hours': peak_hours.to_dict(),
                        'weekday_distribution': {weekday_names[k]: v for k, v in weekday_counts.items()}
                    }
        except Exception as e:
            print(f"⚠️ 时间模式分析失败: {e}")
        return None

    def detect_suspicious_patterns(self, df, display_limit=50):
        """检测可疑模式和异常（优化版）"""
        if df is None or df.empty:
            print("❌ 无法检测可疑模式：无数据")
            return None

        print("\n🚨 可疑模式检测")
        print("=" * 60)

        alerts = []
        current_time = datetime.now()

        # 预先计算常用数据
        print("📊 预处理数据...")
        valid_df = df.dropna(subset=['insert_day']).copy()
        if 'final_app_name' in valid_df.columns:
            valid_df = valid_df.dropna(subset=['final_app_name'])

        print(f"   有效数据: {len(valid_df)} 条")

        # 1. 检测案件数异常增长的APP（使用7天历史平均）
        print("🔍 检测案件数异常增长...")
        if 'final_app_name' in valid_df.columns and len(valid_df) > 0:
            recent_days = 3
            history_days = 7  # 使用7天历史平均
            recent_date = current_time - timedelta(days=recent_days)
            history_start_date = current_time - timedelta(days=history_days + recent_days)
            history_end_date = current_time - timedelta(days=recent_days)

            # 最近3天的案件
            recent_mask = valid_df['insert_day'] >= recent_date
            recent_cases = valid_df[recent_mask]

            # 历史7天的案件
            history_mask = (valid_df['insert_day'] >= history_start_date) & (valid_df['insert_day'] < history_end_date)
            history_cases = valid_df[history_mask]

            if len(recent_cases) > 0 and len(history_cases) > 0:
                recent_app_counts = recent_cases['final_app_name'].value_counts()
                significant_apps = recent_app_counts[recent_app_counts >= 3].index

                if len(significant_apps) > 0:
                    history_daily_avg = history_cases['final_app_name'].value_counts() / history_days

                    for app in significant_apps:
                        recent_count = recent_app_counts[app]
                        historical_avg = history_daily_avg.get(app, 0) * recent_days

                        if historical_avg > 0 and recent_count > historical_avg * self.alert_thresholds['case_spike']:
                            spike_ratio = recent_count / historical_avg
                            alerts.append({
                                'type': '案件数激增',
                                'app_name': app,
                                'severity': '高' if spike_ratio > 5 else '中',
                                'description': f'{app} 最近{recent_days}天案件数({recent_count})较7天历史平均({historical_avg:.1f})增长{spike_ratio:.1f}倍'
                            })

        # 2. 检测金额异常的案件（优化版）
        print("🔍 检测金额异常...")
        if 'involved_amount' in valid_df.columns and 'final_app_name' in valid_df.columns:
            amount_df = valid_df.dropna(subset=['involved_amount'])
            if len(amount_df) > 0:
                app_case_counts = amount_df['final_app_name'].value_counts()
                significant_apps = app_case_counts[app_case_counts >= 5].index[:20]

                for app in significant_apps:
                    app_amounts = amount_df[amount_df['final_app_name'] == app]['involved_amount']
                    if len(app_amounts) >= 5:
                        mean_amount = app_amounts.mean()
                        std_amount = app_amounts.std()

                        if std_amount > 0:
                            threshold = mean_amount + 2 * std_amount
                            high_amount_cases = app_amounts[app_amounts > threshold]

                            if len(high_amount_cases) > 0:
                                max_amount = high_amount_cases.max()
                                alerts.append({
                                    'type': '异常高额',
                                    'app_name': app,
                                    'severity': '高' if max_amount > self.alert_thresholds[
                                    'high_risk_amount'] else '中',
                                    'description': f'{app} 出现异常高额案件，最高{max_amount:,.0f}元（正常范围:{mean_amount:,.0f}±{std_amount:,.0f}元）'
                                })

        # 3. 检测相似APP名称（大幅优化）
        print("🔍 检测APP变种...")
        if 'final_app_name' in valid_df.columns:
            app_names = valid_df['final_app_name'].unique()

            # 只处理案件数较多的APP，减少计算量
            app_counts = valid_df['final_app_name'].value_counts()
            top_apps = app_counts.head(50).index.tolist()  # 只检查TOP50

            similar_groups = self._find_similar_apps_optimized(top_apps)

            for group in similar_groups:
                if len(group) >= 3:
                    total_cases = sum(app_counts.get(app, 0) for app in group)
                    alerts.append({
                        'type': 'APP变种',
                        'app_name': ', '.join(group[:3]) + ('...' if len(group) > 3 else ''),
                        'severity': '中' if total_cases > 20 else '低',
                        'description': f'发现{len(group)}个疑似变种APP，总案件数{total_cases}起',
                        'recommendation': '分析是否为同一诈骗团伙使用多个相似APP名称'
                    })

        # 4. 检测集中爆发的地区（简化版）
        print("🔍 检测地区集中...")
        if 'occurrence_area' in valid_df.columns:
            recent_week = current_time - timedelta(days=7)
            recent_area_cases = valid_df[valid_df['insert_day'] >= recent_week]

            if len(recent_area_cases) > 0:
                area_counts = recent_area_cases['occurrence_area'].value_counts().head(5)
                total_recent = len(recent_area_cases)

                for area, count in area_counts.items():
                    if count > total_recent * 0.3:
                        alerts.append({
                            'type': '地区集中',
                            'app_name': area,
                            'severity': '中',
                            'description': f'{area} 最近7天案件数达{count}起，占比{count / total_recent * 100:.1f}%',
                            'recommendation': '联系当地警方，可能存在本地化诈骗活动'
                        })

        # 输出结果（保持原有逻辑）
        if alerts:
            total_alerts = len(alerts)
            severity_order = {'高': 3, '中': 2, '低': 1}
            alerts.sort(key=lambda x: severity_order.get(x['severity'], 0), reverse=True)

            display_alerts = alerts[:display_limit]

            if total_alerts > display_limit:
                print(f"🚨 发现 {total_alerts} 个可疑模式，显示TOP{display_limit}个:")
            else:
                print(f"🚨 发现 {total_alerts} 个可疑模式:")

            for i, alert in enumerate(display_alerts, 1):
                severity_emoji = {'高': '🔴', '中': '🟡', '低': '🟢'}.get(alert['severity'], '⚪')
                print(f"{i:2d}. [{alert['type']}] {alert['app_name']} {severity_emoji}")
                print(f"    {alert['description']}")
                # 去掉建议字段

        else:
            print("✅ 未发现明显的可疑模式")

        return alerts

    def _find_similar_apps_optimized(self, app_names, similarity_threshold=0.8):
        """优化的相似APP查找（大幅提升性能）"""
        if len(app_names) == 0:
            return []

        print(f"   处理 {len(app_names)} 个APP...")

        similar_groups = []
        processed = set()

        # 预处理：按长度和首字符分组，减少比较次数
        app_groups = defaultdict(list)
        for app in app_names:
            if app and len(app) > 0:
                key = (len(app), app[0].lower())
                app_groups[key].append(app)

        # 只在相同组内比较
        for group_key, group_apps in app_groups.items():
            if len(group_apps) < 2:
                continue

            for i, app1 in enumerate(group_apps):
                if app1 in processed:
                    continue

                similar_group = [app1]
                processed.add(app1)

                # 只与后续的APP比较，避免重复
                for app2 in group_apps[i + 1:]:
                    if app2 in processed:
                        continue

                    # 快速预筛选：长度差异太大直接跳过
                    if abs(len(app1) - len(app2)) > 2:
                        continue

                    # 使用更快的相似度计算
                    similarity = self._fast_similarity(app1.lower(), app2.lower())
                    if similarity >= similarity_threshold:
                        similar_group.append(app2)
                        processed.add(app2)

                if len(similar_group) > 1:
                    similar_groups.append(similar_group)

        return similar_groups

    def _fast_similarity(self, s1, s2):
        """快速相似度计算"""
        if s1 == s2:
            return 1.0

        # 使用简单的字符集合交集比例，比SequenceMatcher快很多
        set1 = set(s1)
        set2 = set(s2)

        if len(set1) == 0 and len(set2) == 0:
            return 1.0

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def detect_suspicious_patterns(self, df, display_limit=50):
        """检测可疑模式和异常"""
        if df is None or df.empty:
            print("❌ 无法检测可疑模式：无数据")
            return None

        print("\n🚨 可疑模式检测")
        print("=" * 60)

        alerts = []

        # 1. 检测案件数异常增长的APP
        if 'final_app_name' in df.columns and 'insert_day' in df.columns:
            recent_days = 3
            recent_date = datetime.now() - timedelta(days=recent_days)

            recent_cases = df[df['insert_day'] >= recent_date]
            recent_app_counts = recent_cases.groupby('final_app_name').size()

            # 近7天历史平均
            history_start_date = datetime.now() - timedelta(days=10)  # 7天历史 + 3天最近
            history_end_date = datetime.now() - timedelta(days=recent_days)
            historical_cases = df[(df['insert_day'] >= history_start_date) & (df['insert_day'] < history_end_date)]

            if not historical_cases.empty:
                historical_daily_avg = historical_cases.groupby('final_app_name').size() / 7  # 7天平均

                for app in recent_app_counts.index:
                    recent_count = recent_app_counts[app]
                    historical_avg = historical_daily_avg.get(app, 0) * recent_days

                    if historical_avg > 0 and recent_count > historical_avg * self.alert_thresholds['case_spike']:
                        spike_ratio = recent_count / historical_avg
                        alerts.append({
                            'type': '案件数激增',
                            'app_name': app,
                            'severity': '高' if spike_ratio > 5 else '中',
                            'description': f'{app} 最近{recent_days}天案件数({recent_count})较近7天案件平均数({historical_avg:.1f})增长{spike_ratio:.1f}倍'
                        })

        # 2. 检测金额异常的案件
        if 'involved_amount' in df.columns and 'final_app_name' in df.columns:
            app_amount_stats = df.groupby('final_app_name')['involved_amount'].agg(['mean', 'std', 'count'])
            app_amount_stats = app_amount_stats[app_amount_stats['count'] >= 5]

            for app, stats in app_amount_stats.iterrows():
                app_cases = df[df['final_app_name'] == app]['involved_amount'].dropna()
                if not app_cases.empty:
                    # 检测异常高额案件
                    threshold = stats['mean'] + 2 * stats['std'] if stats['std'] > 0 else stats['mean'] * 2
                    high_amount_cases = app_cases[app_cases > threshold]

                    if len(high_amount_cases) > 0:
                        max_amount = high_amount_cases.max()
                        alerts.append({
                            'type': '异常高额',
                            'app_name': app,
                            'severity': '高' if max_amount > self.alert_thresholds['high_risk_amount'] else '中',
                            'description': f'{app} 出现异常高额案件，最高{max_amount:,.0f}元（正常范围:{stats["mean"]:,.0f}±{stats["std"]:,.0f}元）'
                        })

        # 3. 检测相似APP名称（可能的变种）
        if 'final_app_name' in df.columns:
            app_names = df['final_app_name'].dropna().unique()
            similar_groups = self._find_similar_apps(app_names)

            for group in similar_groups:
                if len(group) >= 3:  # 至少3个相似APP
                    total_cases = sum(df[df['final_app_name'] == app].shape[0] for app in group)
                    alerts.append({
                        'type': 'APP变种',
                        'app_name': ', '.join(group[:3]) + ('...' if len(group) > 3 else ''),
                        'severity': '中' if total_cases > 20 else '低',
                        'description': f'发现{len(group)}个疑似变种APP，总案件数{total_cases}起'
                    })

        # 4. 检测集中爆发的地区
        if 'occurrence_area' in df.columns:
            recent_area_cases = df[df['insert_day'] >= (datetime.now() - timedelta(days=7))]
            area_counts = recent_area_cases['occurrence_area'].value_counts().head(5)

            total_recent = len(recent_area_cases)
            for area, count in area_counts.items():
                if count > total_recent * 0.3:  # 超过30%的案件集中在一个地区
                    alerts.append({
                        'type': '地区集中',
                        'app_name': area,
                        'severity': '中',
                        'description': f'{area} 最近7天案件数达{count}起，占比{count / total_recent * 100:.1f}%'
                    })

        # 输出预警信息
        if alerts:
            total_alerts = len(alerts)

            # 按严重程度排序
            severity_order = {'高': 3, '中': 2, '低': 1}
            alerts.sort(key=lambda x: severity_order.get(x['severity'], 0), reverse=True)

            # 限制显示数量
            display_alerts = alerts[:display_limit]

            if total_alerts > display_limit:
                print(f"🚨 发现 {total_alerts} 个可疑模式，显示TOP{display_limit}个:")
            else:
                print(f"🚨 发现 {total_alerts} 个可疑模式:")

            for i, alert in enumerate(display_alerts, 1):
                severity_emoji = {'高': '🔴', '中': '🟡', '低': '🟢'}.get(alert['severity'], '⚪')
                print(f"{i:2d}. [{alert['type']}] {alert['app_name']} {severity_emoji}")
                print(f"    {alert['description']}")
                # 去掉建议字段

            # 如果有未显示的预警，提供统计信息
            if total_alerts > display_limit:
                remaining_alerts = alerts[display_limit:]
                remaining_high = len([alert for alert in remaining_alerts if alert['severity'] == '高'])
                remaining_medium = len([alert for alert in remaining_alerts if alert['severity'] == '中'])
                remaining_low = len([alert for alert in remaining_alerts if alert['severity'] == '低'])
                print(f"\n📊 未显示的{total_alerts - display_limit}个预警严重程度分布:")
                print(f"   高: {remaining_high}个 | 中: {remaining_medium}个 | 低: {remaining_low}个")
        else:
            print("✅ 未发现明显的可疑模式")

        return alerts

    def _find_similar_apps(self, app_names, similarity_threshold=0.8):
        """查找相似的APP名称 - 统一版本"""
        if len(app_names) == 0:
            return []

        from difflib import SequenceMatcher
        similar_groups = []
        processed = set()

        for i, app1 in enumerate(app_names):
            if app1 in processed:
                continue

            similar_group = [app1]
            processed.add(app1)

            for j, app2 in enumerate(app_names[i + 1:], i + 1):
                if app2 in processed:
                    continue

                similarity = SequenceMatcher(None, app1, app2).ratio()
                if similarity >= similarity_threshold:
                    similar_group.append(app2)
                    processed.add(app2)

            if len(similar_group) >= 2:
                similar_groups.append(similar_group)

        return similar_groups

    def create_comprehensive_dashboard(self, df, analysis_results=None):
        """创建综合风险分析报告（HTML格式）"""
        if df is None or df.empty:
            print("❌ 无法创建风险分析报告：无数据")
            return None

        print("\n📊 生成综合风险分析报告")
        print("=" * 60)

        from plotly.subplots import make_subplots
        import plotly.graph_objects as go
        import plotly.express as px

        # 创建HTML模板
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>APP涉案风险分析报告</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                          color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }}
                .section {{ background: white; padding: 20px; margin: 20px 0; 
                           border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .risk-high {{ border-left: 5px solid #e74c3c; }}
                .risk-medium {{ border-left: 5px solid #f39c12; }}
                .risk-low {{ border-left: 5px solid #27ae60; }}
                .risk-normal {{ border-left: 5px solid #3498db; }}
                .metric {{ display: inline-block; margin: 10px; padding: 15px; 
                         background: #ecf0f1; border-radius: 8px; text-align: center; }}
                .alert-item {{ padding: 10px; margin: 5px 0; border-radius: 5px; }}
                .alert-high {{ background-color: #ffebee; border-left: 4px solid #f44336; }}
                .alert-medium {{ background-color: #fff3e0; border-left: 4px solid #ff9800; }}
                .alert-low {{ background-color: #e8f5e8; border-left: 4px solid #4caf50; }}
                .chart-container {{ margin: 20px 0; }}
                table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                th, td {{ padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🛡️ APP涉案风险分析报告</h1>
                <p>生成时间: {generation_time} | 数据周期: {data_period}</p>
            </div>

            {risk_summary_section}
            {key_metrics_section}
            {charts_section}
            {alerts_section}
            {new_apps_section}
            {network_analysis_section}
            {recommendations_section}
        </body>
        </html>
        """

        # 生成各个部分的内容
        generation_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        data_period = f"{df['insert_day'].min().strftime('%Y-%m-%d')} 至 {df['insert_day'].max().strftime('%Y-%m-%d')}"

        # 风险评估部分
        risk_summary_section = self._generate_risk_summary_html(df, analysis_results)

        # 关键指标部分
        key_metrics_section = self._generate_key_metrics_html(df)

        # 图表部分
        charts_section = self._generate_charts_html(df)

        # 预警信息部分
        alerts_section = self._generate_alerts_html(analysis_results)

        # 新APP分析部分
        new_apps_section = self._generate_new_apps_html(analysis_results)

        # 通联关系分析部分（整合到报告中）
        network_analysis_section = self._generate_network_analysis_html(df)

        # 建议措施部分
        recommendations_section = self._generate_recommendations_html(analysis_results)

        # 组装HTML
        html_content = html_template.format(
            generation_time=generation_time,
            data_period=data_period,
            risk_summary_section=risk_summary_section,
            key_metrics_section=key_metrics_section,
            charts_section=charts_section,
            alerts_section=alerts_section,
            new_apps_section=new_apps_section,
            network_analysis_section=network_analysis_section,
            recommendations_section=recommendations_section
        )

        # 保存HTML文件
        output_file = self.export_dir / f"风险分析报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"📊 风险分析报告已保存: {output_file}")
        return output_file

    def _generate_risk_summary_html(self, df, analysis_results):
        """生成风险摘要HTML"""
        if analysis_results and 'risk_report' in analysis_results:
            risk_report = analysis_results['risk_report']
            risk_level = risk_report.get('risk_level', '未知')
            risk_score = risk_report.get('risk_score', 0)

            # 根据风险等级设置样式
            risk_class = 'risk-normal'
            if risk_level == '高风险':
                risk_class = 'risk-high'
            elif risk_level == '中风险':
                risk_class = 'risk-medium'
            elif risk_level == '低风险':
                risk_class = 'risk-low'

            html = f"""
            <div class="section {risk_class}">
                <h2>⚠️ 风险评估摘要</h2>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <h3>总体风险等级: {risk_level}</h3>
                        <p>风险评分: {risk_score:.1f}/100</p>
                        <p>数据周期: {risk_report.get('data_period', '未知')}</p>
                        <p>案件总数: {risk_report.get('total_cases', 0):,} 起</p>
                        <p>涉案总金额: {risk_report.get('total_amount', 0):,.2f} 元</p>
                    </div>
                    <div style="text-align: center;">
                        <div style="width: 150px; height: 150px; border-radius: 50%; background: conic-gradient(
                            #e74c3c 0% {min(risk_score, 100)}%, 
                            #ecf0f1 {min(risk_score, 100)}% 100%
                        ); display: flex; justify-content: center; align-items: center;">
                            <div style="width: 120px; height: 120px; border-radius: 50%; background: white; 
                                        display: flex; justify-content: center; align-items: center;">
                                <span style="font-size: 24px; font-weight: bold;">{risk_score:.1f}</span>
                            </div>
                        </div>
                        <p>风险评分</p>
                    </div>
                </div>
            </div>
            """
        else:
            html = """
            <div class="section">
                <h2>⚠️ 风险评估摘要</h2>
                <p>未生成风险报告或风险评估数据不完整</p>
            </div>
            """

        return html

    def _generate_key_metrics_html(self, df):
        """生成关键指标HTML"""
        total_cases = len(df)
        unique_apps = df['final_app_name'].nunique()
        total_amount = df['involved_amount'].sum() if 'involved_amount' in df.columns else 0
        avg_amount = df['involved_amount'].mean() if 'involved_amount' in df.columns else 0

        # 计算高额案件
        high_amount_cases = len(df[df['involved_amount'] >= 100000]) if 'involved_amount' in df.columns else 0
        high_amount_percentage = (high_amount_cases / total_cases * 100) if total_cases > 0 else 0

        html = """
        <div class="section">
            <h2>📊 关键指标</h2>
            <div style="display: flex; flex-wrap: wrap; justify-content: space-between;">
        """

        # 添加关键指标卡片
        html += f"""
                <div class="metric" style="width: 22%;">
                    <h3>{total_cases:,}</h3>
                    <p>案件总数</p>
                </div>
                <div class="metric" style="width: 22%;">
                    <h3>{unique_apps:,}</h3>
                    <p>涉案APP数</p>
                </div>
                <div class="metric" style="width: 22%;">
                    <h3>{total_amount:,.2f}</h3>
                    <p>涉案总金额(元)</p>
                </div>
                <div class="metric" style="width: 22%;">
                    <h3>{high_amount_cases:,} ({high_amount_percentage:.1f}%)</h3>
                    <p>高额案件(≥10万)</p>
                </div>
        """

        html += """
            </div>
        </div>
        """

        return html

    def _generate_charts_html(self, df):
        """生成图表HTML"""
        import plotly.graph_objects as go
        from plotly.subplots import make_subplots
        import plotly.express as px
        import json

        html = """
        <div class="section">
            <h2>📈 数据可视化</h2>
        """

        # 1. 时间趋势图
        daily_stats = df.groupby(df['insert_day'].dt.date).agg({
            'case_number': 'count',
            'involved_amount': 'sum'
        }).reset_index()

        fig1 = make_subplots(specs=[[{"secondary_y": True}]])

        fig1.add_trace(
            go.Scatter(x=daily_stats['insert_day'], y=daily_stats['case_number'],
                       name='案件数', line=dict(color='#3498db')),
            secondary_y=False
        )

        fig1.add_trace(
            go.Scatter(x=daily_stats['insert_day'], y=daily_stats['involved_amount'],
                       name='涉案金额', line=dict(color='#e74c3c')),
            secondary_y=True
        )

        fig1.update_layout(
            title='案件数量和涉案金额时间趋势',
            xaxis_title='日期',
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
        )

        fig1.update_yaxes(title_text="案件数", secondary_y=False)
        fig1.update_yaxes(title_text="涉案金额(元)", secondary_y=True)

        # 2. APP分布图
        app_counts = df['final_app_name'].value_counts().head(15)

        fig2 = go.Figure(go.Bar(
            x=app_counts.values,
            y=app_counts.index,
            orientation='h',
            marker_color='#9b59b6'
        ))

        fig2.update_layout(
            title='TOP15 涉案APP分布',
            xaxis_title='案件数',
            yaxis_title='APP名称',
            height=500
        )

        # 3. 地区分布图
        if 'occurrence_area' in df.columns:
            area_counts = df['occurrence_area'].value_counts().head(10)

            fig3 = go.Figure(go.Bar(
                x=area_counts.index,
                y=area_counts.values,
                marker_color='#2ecc71'
            ))

            fig3.update_layout(
                title='TOP10 发案地区分布',
                xaxis_title='地区',
                yaxis_title='案件数'
            )

        # 将图表转换为HTML
        chart1_html = fig1.to_html(full_html=False, include_plotlyjs='cdn')
        chart2_html = fig2.to_html(full_html=False, include_plotlyjs='cdn')

        html += f"""
            <div class="chart-container">
                {chart1_html}
            </div>
            <div class="chart-container">
                {chart2_html}
            </div>
        """

        if 'occurrence_area' in df.columns:
            chart3_html = fig3.to_html(full_html=False, include_plotlyjs='cdn')
            html += f"""
                <div class="chart-container">
                    {chart3_html}
                </div>
            """

        html += """
        </div>
        """

        return html

    def _generate_alerts_html(self, analysis_results):
        """生成预警信息HTML"""
        html = """
        <div class="section">
            <h2>🚨 预警信息</h2>
        """

        if analysis_results and 'alerts' in analysis_results and analysis_results['alerts']:
            alerts = analysis_results['alerts']

            html += f"<p>共发现 {len(alerts)} 个可疑模式</p>"

            # 按严重程度排序
            severity_order = {'高': 3, '中': 2, '低': 1}
            sorted_alerts = sorted(alerts, key=lambda x: severity_order.get(x.get('severity', '低'), 0), reverse=True)

            for alert in sorted_alerts[:20]:  # 显示前20个预警
                severity = alert.get('severity', '低')
                alert_class = f"alert-{severity.lower()}" if severity in ['高', '中', '低'] else "alert-low"
                severity_emoji = {'高': '🔴', '中': '🟡', '低': '🟢'}.get(severity, '⚪')

                html += f"""
                <div class="alert-item {alert_class}">
                    <strong>{severity_emoji} [{alert.get('type', '未知类型')}]</strong> {alert.get('app_name', '未知APP')}
                    <p>{alert.get('description', '无描述')}</p>
                </div>
                """
        else:
            html += "<p>未发现明显的可疑模式</p>"

        html += """
        </div>
        """

        return html

    def _generate_new_apps_html(self, analysis_results):
        """生成新APP分析HTML"""
        html = """
        <div class="section">
            <h2>🆕 新APP分析</h2>
        """

        if analysis_results and 'new_apps' in analysis_results and isinstance(analysis_results['new_apps'], list) and \
                analysis_results['new_apps']:
            new_apps = analysis_results['new_apps']

            html += f"<p>最近发现 {len(new_apps)} 个新APP</p>"

            # 创建表格
            html += """
            <table>
                <tr>
                    <th>APP名称</th>
                    <th>案件数</th>
                    <th>涉案金额(元)</th>
                    <th>首次出现</th>
                    <th>风险等级</th>
                </tr>
            """

            for app in new_apps[:15]:  # 显示前15个新APP
                risk_color = {
                    '高': '#e74c3c',
                    '中': '#f39c12',
                    '低': '#27ae60'
                }.get(app.get('risk_level', '低'), '#3498db')

                html += f"""
                <tr>
                    <td>{app.get('app_name', '未知')}</td>
                    <td>{app.get('case_count', 0)}</td>
                    <td>{app.get('total_amount', 0):,.2f}</td>
                    <td>{app.get('first_seen', '未知')}</td>
                    <td style="color: {risk_color}; font-weight: bold;">{app.get('risk_level', '低')}</td>
                </tr>
                """

            html += """
            </table>
            """
        else:
            html += "<p>未发现新APP</p>"

        html += """
        </div>
        """

        return html

    def _generate_network_analysis_html(self, df):
        """生成通联关系分析HTML（整合到报告中）"""
        # 执行通联关系分析
        network_results = self.analyze_network_relationships(df)

        html = """
        <div class="section">
            <h2>🕸️ 通联关系分析</h2>
        """

        # APP集群信息
        html += "<h3>APP集群分析</h3>"

        if network_results and network_results.get('app_clusters'):
            clusters = network_results['app_clusters']
            if clusters:
                html += "<div style='display: flex; flex-wrap: wrap;'>"

                for cluster_name, cluster_info in list(clusters.items())[:6]:  # 显示前6个集群
                    html += f"""
                    <div style="width: 48%; margin: 1%;" class="alert-item alert-medium">
                        <strong>{cluster_info['type']}</strong>
                        <p>{cluster_info['description']}</p>
                        <p>涉及APP: {', '.join(cluster_info['apps'])}</p>
                    </div>
                    """

                html += "</div>"
            else:
                html += "<p>未发现明显的APP集群</p>"
        else:
            html += "<p>未执行APP集群分析</p>"

        # 可疑网络
        html += "<h3>可疑网络识别</h3>"

        if network_results and network_results.get('suspicious_networks'):
            networks = network_results['suspicious_networks']
            if networks:
                for network in networks[:8]:  # 显示前8个网络
                    risk_class = f"alert-{network['risk_level'].lower()}" if 'risk_level' in network and network[
                        'risk_level'] in ['高', '中', '低'] else "alert-medium"

                    html += f"""
                    <div class="alert-item {risk_class}">
                        <strong>{network.get('type', '未知类型')}</strong>
                        <p>{network.get('description', '无描述')}</p>
                        <p>涉及APP: {', '.join(network.get('apps', []))}</p>
                    </div>
                    """
            else:
                html += "<p>未发现可疑网络</p>"
        else:
            html += "<p>未执行可疑网络分析</p>"

        # 跨APP受害人分析
        if network_results and network_results.get('cross_app_victims'):
            cross_data = network_results['cross_app_victims']
            if cross_data:
                html += f"""
                <h3>跨APP受害人分析</h3>
                <p>多APP受害人数量: {cross_data.get('multi_app_victims_count', 0)}</p>
                <p>平均每位受害人涉及APP数: {cross_data.get('avg_apps_per_victim', 0):.2f}</p>
                """

                if 'top_app_combinations' in cross_data and cross_data['top_app_combinations']:
                    html += "<p>常见APP组合:</p><ul>"
                    for combo, count in cross_data['top_app_combinations']:
                        html += f"<li>{' + '.join(combo)}: {count}人</li>"
                    html += "</ul>"

        html += """
        </div>
        """

        return html

    def _generate_recommendations_html(self, analysis_results):
        """生成建议措施HTML"""
        html = """
        <div class="section">
            <h2>💡 建议措施</h2>
        """

        if analysis_results and 'risk_report' in analysis_results and 'recommendations' in analysis_results[
            'risk_report']:
            recommendations = analysis_results['risk_report']['recommendations']

            html += "<ul>"
            for recommendation in recommendations:
                html += f"<li>{recommendation}</li>"
            html += "</ul>"
        else:
            # 默认建议
            html += """
            <ul>
                <li>持续监控新出现的APP，特别关注案件数快速增长的APP</li>
                <li>对高风险APP建立快速响应机制，及时采取防控措施</li>
                <li>加强对通联关系明显的APP群组的联合监控</li>
                <li>定期分析案件数据趋势，优化预警阈值</li>
            </ul>
            """

        html += """
        </div>
        """

        return html

    # def generate_risk_report(self, df, analysis_results=None):
    #     """生成风险评估报告"""
    #     if df is None or df.empty:
    #         print("❌ 无法生成风险报告：无数据")
    #         return None
    #
    #     print("\n📋 生成风险评估报告")
    #     print("=" * 60)
    #
    #     report = {
    #         'generated_time': datetime.now(),
    #         'data_period': f"{df['insert_day'].min().strftime('%Y-%m-%d')} 至 {df['insert_day'].max().strftime('%Y-%m-%d')}",
    #         'total_cases': len(df),
    #         'total_amount': df['involved_amount'].sum() if 'involved_amount' in df.columns else 0,
    #         'unique_apps': df['final_app_name'].nunique() if 'final_app_name' in df.columns else 0,
    #         'sections': {}
    #     }
    #
    #     # 1. 使用已有分析结果或重新执行分析
    #     if analysis_results:
    #         print("🔍 使用已有分析结果...")
    #         # 使用传入的分析结果
    #         if analysis_results.get('new_apps') is not None:
    #             report['sections']['new_apps'] = analysis_results['new_apps'].to_dict('records') if hasattr(
    #                 analysis_results['new_apps'], 'to_dict') else analysis_results['new_apps']
    #
    #         if analysis_results.get('alerts'):
    #             report['sections']['alerts'] = analysis_results['alerts']
    #
    #         if analysis_results.get('financial') is not None:
    #             financial_analysis = analysis_results['financial']
    #             amount_summary = {
    #                 'total_amount': financial_analysis['involved_amount'].sum(),
    #                 'avg_amount': financial_analysis['involved_amount'].mean(),
    #                 'high_risk_cases': len(financial_analysis[
    #                                            financial_analysis['involved_amount'] >= self.alert_thresholds[
    #                                                'high_risk_amount']])
    #             }
    #             report['sections']['financial'] = amount_summary
    #
    #         if analysis_results.get('fraud_methods'):
    #             fraud_methods = analysis_results['fraud_methods']
    #             report['sections']['fraud_methods'] = {
    #                 'top_main_types': fraud_methods.get('main_types', pd.Series()).head(5).to_dict(),
    #                 'top_sub_types': fraud_methods.get('sub_types', pd.Series()).head(5).to_dict()
    #             }
    #     else:
    #         # 重新执行分析（保持原有逻辑）
    #         print("🔍 执行新APP检测...")
    #         new_apps = self.detect_new_apps(df)
    #         if new_apps is not None:
    #             report['sections']['new_apps'] = new_apps.to_dict('records')
    #
    #         print("🔍 执行可疑模式检测...")
    #         alerts = self.detect_suspicious_patterns(df)
    #         if alerts:
    #             report['sections']['alerts'] = alerts
    #
    #         print("🔍 执行资金分析...")
    #         financial_analysis = self.analyze_financial_impact(df)
    #         if financial_analysis is not None:
    #             amount_summary = {
    #                 'total_amount': financial_analysis['involved_amount'].sum(),
    #                 'avg_amount': financial_analysis['involved_amount'].mean(),
    #                 'high_risk_cases': len(financial_analysis[
    #                                            financial_analysis['involved_amount'] >= self.alert_thresholds[
    #                                                'high_risk_amount']])
    #             }
    #             report['sections']['financial'] = amount_summary
    #
    #         print("🔍 执行手段分析...")
    #         fraud_methods = self.analyze_fraud_methods(df)
    #         if fraud_methods:
    #             report['sections']['fraud_methods'] = {
    #                 'top_main_types': fraud_methods.get('main_types', pd.Series()).head(5).to_dict(),
    #                 'top_sub_types': fraud_methods.get('sub_types', pd.Series()).head(5).to_dict()
    #             }
    #
    #     # 2. 风险等级评估
    #     risk_score = self._calculate_risk_score(report)
    #     report['risk_level'] = self._get_risk_level(risk_score)
    #     report['risk_score'] = risk_score
    #
    #     # 3. 生成建议
    #     report['recommendations'] = self._generate_recommendations(report)
    #
    #     # 4. 保存报告
    #     report_file = self.export_dir / f"risk_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    #     with open(report_file, 'w', encoding='utf-8') as f:
    #         json.dump(report, f, ensure_ascii=False, indent=2, default=str)
    #
    #     print(f"📋 风险报告已保存: {report_file}")
    #     print(f"🎯 总体风险等级: {report['risk_level']} (评分: {risk_score:.1f}/100)")
    #
    #     return report

    def create_database_indexes(self):
        """创建数据库索引（兼容旧版MySQL）"""
        if not self.connect_database():
            print("❌ 无法连接数据库，索引创建失败")
            return False

        # 索引定义：(索引名, 表名, 列名)
        indexes = [
            ('idx_insert_day', 'anti_fraud_case_new', 'insert_day'),
            ('idx_app_name', 'anti_fraud_case_new', 'app_name'),
            ('idx_service_account_app_name', 'anti_fraud_case_new', 'service_account_app_name'),
            ('idx_involved_amount', 'anti_fraud_case_new', 'involved_amount'),
            ('idx_insert_day_app_name', 'anti_fraud_case_new', 'insert_day, app_name'),
            ('idx_insert_day_amount', 'anti_fraud_case_new', 'insert_day, involved_amount'),
            ('idx_case_main_type', 'anti_fraud_case_new', 'case_main_type'),
            ('idx_occurrence_area', 'anti_fraud_case_new', 'occurrence_area'),
        ]

        try:
            with self.engine.connect() as conn:
                for i, (index_name, table_name, columns) in enumerate(indexes, 1):
                    print(f"📊 检查索引 {i}/{len(indexes)}: {index_name}...")

                    # 检查索引是否已存在
                    check_sql = text("""
                        SELECT COUNT(*) as count 
                        FROM information_schema.statistics 
                        WHERE table_schema = DATABASE() 
                        AND table_name = :table_name 
                        AND index_name = :index_name
                    """)

                    result = conn.execute(check_sql, {
                        'table_name': table_name,
                        'index_name': index_name
                    })

                    index_exists = result.fetchone()[0] > 0

                    if index_exists:
                        print(f"   ✅ 索引 {index_name} 已存在，跳过")
                    else:
                        # 创建索引
                        create_sql = text(f"CREATE INDEX {index_name} ON {table_name}({columns})")
                        conn.execute(create_sql)
                        conn.commit()
                        print(f"   ✅ 索引 {index_name} 创建成功")

                # 分析表统计信息
                print("📊 更新表统计信息...")
                conn.execute(text("ANALYZE TABLE anti_fraud_case_new"))
                conn.commit()

            print("✅ 数据库索引检查和创建完成")
            return True

        except Exception as e:
            print(f"❌ 索引创建失败: {e}")
            return False

    def check_existing_indexes(self):
        """检查现有索引"""
        if not self.connect_database():
            return None

        try:
            with self.engine.connect() as conn:
                query = text("""
                    SELECT 
                        index_name,
                        column_name,
                        seq_in_index,
                        non_unique
                    FROM information_schema.statistics 
                    WHERE table_schema = DATABASE() 
                    AND table_name = 'anti_fraud_case_new'
                    ORDER BY index_name, seq_in_index
                """)

                result = conn.execute(query)
                indexes = result.fetchall()

                if indexes:
                    # print("📊 现有索引列表:")
                    current_index = None
                    for row in indexes:
                        if row[0] != current_index:
                            index_type = "唯一" if row[3] == 0 else "普通"
                            # print(f"  {row[0]} ({index_type}): {row[1]}", end="")
                            current_index = row[0]
                #         else:
                #             # print(f", {row[1]}", end="")
                #     # print()  # 换行
                # else:
                #     # print("⚠️ 未找到任何索引")

            return indexes

        except Exception as e:
            print(f"❌ 检查索引失败: {e}")
            return None

    def drop_index_if_exists(self, index_name, table_name):
        """安全删除索引（如果存在）"""
        if not self.connect_database():
            return False

        try:
            with self.engine.connect() as conn:
                # 检查索引是否存在
                check_sql = text("""
                    SELECT COUNT(*) as count 
                    FROM information_schema.statistics 
                    WHERE table_schema = DATABASE() 
                    AND table_name = :table_name 
                    AND index_name = :index_name
                """)

                result = conn.execute(check_sql, {
                    'table_name': table_name,
                    'index_name': index_name
                })

                if result.fetchone()[0] > 0:
                    drop_sql = text(f"DROP INDEX {index_name} ON {table_name}")
                    conn.execute(drop_sql)
                    conn.commit()
                    print(f"✅ 索引 {index_name} 已删除")
                    return True
                else:
                    print(f"ℹ️ 索引 {index_name} 不存在")
                    return True

        except Exception as e:
            print(f"❌ 删除索引失败: {e}")
            return False

    def rebuild_all_indexes(self):
        """重建所有索引"""
        print("🔄 重建所有索引...")

        # 先删除现有索引
        indexes_to_drop = [
            'idx_insert_day', 'idx_app_name', 'idx_service_account_app_name',
            'idx_involved_amount', 'idx_insert_day_app_name', 'idx_insert_day_amount',
            'idx_case_main_type', 'idx_occurrence_area'
        ]

        for index_name in indexes_to_drop:
            self.drop_index_if_exists(index_name, 'anti_fraud_case_new')

        # 重新创建索引
        return self.create_database_indexes()

    def get_index_usage_stats(self):
        """获取索引使用统计"""
        if not self.connect_database():
            return None

        try:
            with self.engine.connect() as conn:
                # MySQL 5.7+ 支持
                query = text("""
                    SELECT 
                        object_schema,
                        object_name,
                        index_name,
                        count_read,
                        count_write,
                        count_fetch,
                        count_insert,
                        count_update,
                        count_delete
                    FROM performance_schema.table_io_waits_summary_by_index_usage
                    WHERE object_schema = DATABASE()
                    AND object_name = 'anti_fraud_case_new'
                    ORDER BY count_read DESC
                """)

                result = conn.execute(query)
                stats = result.fetchall()

                if stats:
                    print("📊 索引使用统计:")
                    print("索引名称".ljust(30) + "读取次数".ljust(15) + "写入次数")
                    print("-" * 60)
                    for row in stats:
                        index_name = row[2] or 'PRIMARY'
                        read_count = row[3] or 0
                        write_count = (row[5] or 0) + (row[6] or 0) + (row[7] or 0)
                        print(f"{index_name:<30} {read_count:<15} {write_count}")
                else:
                    print("⚠️ 无法获取索引使用统计（可能需要MySQL 5.7+）")

            return stats

        except Exception as e:
            print(f"⚠️ 获取索引统计失败: {e}")
            print("提示：此功能需要MySQL 5.7+版本和performance_schema启用")
            return None

    def check_query_performance(self):
        """检查查询性能（优化版）"""
        if not self.connect_database():
            return None

        test_queries = [
            {
                'name': '时间范围查询',
                'sql': """
                    SELECT COUNT(*) 
                    FROM anti_fraud_case_new 
                    WHERE DATE(insert_day) BETWEEN DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND CURDATE()
                """
            },
            {
                'name': 'APP统计查询（优化版）',
                'sql': """
                    SELECT app_name, COUNT(*) as cnt 
                    FROM anti_fraud_case_new 
                    WHERE insert_day >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                        AND app_name IS NOT NULL 
                        AND app_name != ''
                    GROUP BY app_name 
                    ORDER BY cnt DESC 
                    LIMIT 10
                """
            },
            {
                'name': '金额分析查询（优化版）',
                'sql': """
                    SELECT COUNT(*), AVG(involved_amount) 
                    FROM anti_fraud_case_new 
                    WHERE insert_day >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                        AND involved_amount > 0
                """
            },
            {
                'name': '复合索引测试',
                'sql': """
                    SELECT app_name, COUNT(*) as cnt, SUM(involved_amount) as total_amount
                    FROM anti_fraud_case_new 
                    WHERE insert_day >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                        AND app_name IS NOT NULL 
                        AND involved_amount > 0
                    GROUP BY app_name 
                    ORDER BY cnt DESC 
                    LIMIT 5
                """
            }
        ]

        print("🔍 查询性能测试")
        print("=" * 50)

        try:
            with self.engine.connect() as conn:
                for query in test_queries:
                    start_time = time.time()
                    result = conn.execute(text(query['sql']))
                    rows = result.fetchall()  # 确保完全执行
                    end_time = time.time()

                    execution_time = end_time - start_time
                    status = "🟢" if execution_time < 1 else "🟡" if execution_time < 5 else "🔴"
                    print(f"{status} {query['name']}: {execution_time:.3f}秒")

                    # 如果查询太慢，给出建议
                    if execution_time > 5:
                        print(f"   ⚠️ 查询较慢，建议检查索引使用情况")

        except Exception as e:
            print(f"❌ 性能测试失败: {e}")

    def analyze_query_execution_plan(self):
        """分析查询执行计划"""
        if not self.connect_database():
            return None

        print("🔍 分析查询执行计划")
        print("=" * 50)

        # 分析慢查询
        slow_query = """
            SELECT app_name, COUNT(*) as cnt 
            FROM anti_fraud_case_new 
            WHERE insert_day >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                AND app_name IS NOT NULL 
            GROUP BY app_name 
            ORDER BY cnt DESC 
            LIMIT 10
        """

        try:
            with self.engine.connect() as conn:
                # 获取执行计划
                explain_query = f"EXPLAIN {slow_query}"
                result = conn.execute(text(explain_query))
                plan = result.fetchall()

                print("📊 执行计划分析:")
                print("表名".ljust(15) + "类型".ljust(10) + "可能的键".ljust(20) + "键".ljust(15) + "行数".ljust(
                    10) + "额外信息")
                print("-" * 90)

                for row in plan:
                    table = str(row[1]) if row[1] else 'N/A'
                    type_val = str(row[2]) if row[2] else 'N/A'
                    possible_keys = str(row[3]) if row[3] else 'N/A'
                    key = str(row[4]) if row[4] else 'N/A'
                    rows = str(row[8]) if row[8] else 'N/A'
                    extra = str(row[9]) if row[9] else 'N/A'

                    print(f"{table:<15} {type_val:<10} {possible_keys:<20} {key:<15} {rows:<10} {extra}")

                    # 性能建议
                    if type_val == 'ALL':
                        print("   🔴 警告: 全表扫描，性能较差")
                    elif 'filesort' in extra.lower():
                        print("   🟡 注意: 使用文件排序，可能影响性能")
                    elif key != 'N/A':
                        print("   🟢 良好: 使用了索引")

        except Exception as e:
            print(f"❌ 执行计划分析失败: {e}")

    def optimize_app_query_performance(self):
        """优化APP查询性能"""
        print("\n⚡ APP查询性能优化建议")
        print("=" * 50)

        suggestions = [
            "1. 使用 insert_day >= DATE 而不是 DATE(insert_day) BETWEEN",
            "2. 确保 app_name 和 insert_day 的复合索引存在",
            "3. 过滤条件尽量使用索引列",
            "4. 考虑分区表（按时间分区）",
            "5. 定期更新表统计信息 (ANALYZE TABLE)"
        ]

        for suggestion in suggestions:
            print(f"💡 {suggestion}")

        # 检查表大小
        if self.connect_database():
            try:
                with self.engine.connect() as conn:
                    size_query = text("""
                        SELECT 
                            table_name,
                            ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size_MB',
                            table_rows
                        FROM information_schema.tables 
                        WHERE table_schema = DATABASE()
                        AND table_name = 'anti_fraud_case_new'
                    """)

                    result = conn.execute(size_query)
                    row = result.fetchone()

                    if row:
                        print(f"\n📊 表信息:")
                        print(f"   表大小: {row[1]} MB")
                        print(f"   估计行数: {row[2]:,}")

                        if row[1] > 1000:  # 大于1GB
                            print("   🔴 表较大，建议考虑分区或归档策略")
                        elif row[1] > 100:  # 大于100MB
                            print("   🟡 表中等大小，注意索引优化")
                        else:
                            print("   🟢 表大小适中")

            except Exception as e:
                print(f"⚠️ 无法获取表大小信息: {e}")

    def create_optimized_indexes(self):
        """创建优化的索引"""
        print("🔧 创建性能优化索引...")

        # 针对性能问题的特殊索引
        optimized_indexes = [
            ('idx_insert_day_app_not_null', 'anti_fraud_case_new', 'insert_day, app_name'),
            ('idx_app_name_not_null', 'anti_fraud_case_new', 'app_name'),
            ('idx_insert_day_desc', 'anti_fraud_case_new', 'insert_day DESC'),
        ]

        if not self.connect_database():
            return False

        try:
            with self.engine.connect() as conn:
                for index_name, table_name, columns in optimized_indexes:
                    # 检查索引是否存在
                    check_sql = text("""
                        SELECT COUNT(*) as count 
                        FROM information_schema.statistics 
                        WHERE table_schema = DATABASE() 
                        AND table_name = :table_name 
                        AND index_name = :index_name
                    """)

                    result = conn.execute(check_sql, {
                        'table_name': table_name,
                        'index_name': index_name
                    })

                    if result.fetchone()[0] == 0:
                        create_sql = text(f"CREATE INDEX {index_name} ON {table_name}({columns})")
                        conn.execute(create_sql)
                        conn.commit()
                        print(f"✅ 优化索引 {index_name} 创建成功")
                    else:
                        print(f"ℹ️ 优化索引 {index_name} 已存在")

            return True

        except Exception as e:
            print(f"❌ 优化索引创建失败: {e}")
            return False

    def optimize_database_settings(self):
        """优化数据库设置建议"""
        print("\n⚙️ 数据库优化建议")
        print("=" * 50)
        print("请在MySQL配置文件(my.cnf)中添加以下配置：")
        print("""
    # 查询缓存
    query_cache_type = 1
    query_cache_size = 256M

    # InnoDB缓冲池
    innodb_buffer_pool_size = 1G

    # 连接数
    max_connections = 200

    # 临时表
    tmp_table_size = 256M
    max_heap_table_size = 256M

    # 排序缓冲
    sort_buffer_size = 2M
    read_buffer_size = 2M
    """)
    def _calculate_risk_score(self, report):
        """计算风险评分"""
        score = 0

        # 新APP风险 (0-30分)
        if 'new_apps' in report['sections']:
            new_apps = report['sections']['new_apps']
            high_risk_new_apps = sum(1 for app in new_apps if app.get('risk_level') == '高')
            score += min(high_risk_new_apps * 10, 30)

        # 预警风险 (0-40分)
        if 'alerts' in report['sections']:
            alerts = report['sections']['alerts']
            high_severity_alerts = sum(1 for alert in alerts if alert.get('severity') == '高')
            medium_severity_alerts = sum(1 for alert in alerts if alert.get('severity') == '中')
            score += min(high_severity_alerts * 15 + medium_severity_alerts * 5, 40)

        # 资金风险 (0-20分)
        if 'financial' in report['sections']:
            financial = report['sections']['financial']
            if financial.get('avg_amount', 0) > 50000:
                score += 10
            if financial.get('high_risk_cases', 0) > 5:
                score += 10

        # 案件量风险 (0-10分)
        if report['total_cases'] > 100:
            score += 5
        if report['total_cases'] > 500:
            score += 5

        return min(score, 100)

    def _get_risk_level(self, score):
        """根据评分确定风险等级"""
        if score >= 70:
            return '高风险'
        elif score >= 40:
            return '中风险'
        elif score >= 20:
            return '低风险'
        else:
            return '正常'

    def _generate_recommendations(self, report):
        """生成建议措施"""
        recommendations = []

        risk_level = report.get('risk_level', '正常')

        if risk_level == '高风险':
            recommendations.extend([
                "立即启动应急响应机制，加强监控频率",
                "重点关注新出现的高风险APP，建立快速封堵机制",
                "加强与运营商和应用商店的联动，及时下架违规APP"
            ])

        if 'new_apps' in report['sections']:
            new_apps = report['sections']['new_apps']
            high_risk_count = sum(1 for app in new_apps if app.get('risk_level') == '高')
            if high_risk_count > 0:
                recommendations.append(f"对{high_risk_count}个高风险新APP进行深度分析，追踪其传播渠道")

        if 'alerts' in report['sections']:
            alerts = report['sections']['alerts']
            alert_types = set(alert.get('type') for alert in alerts)
            if '案件数激增' in alert_types:
                recommendations.append("针对案件激增的APP，分析其推广模式和用户获取渠道")
            if 'APP变种' in alert_types:
                recommendations.append("建立APP变种识别机制，提高相似APP的检测能力")

        if not recommendations:
            recommendations.append("继续保持现有监控力度，定期分析数据趋势")

        return recommendations

    def run_comprehensive_analysis(self, days=30, use_cache=True, fast_mode=False):
        """运行全面分析（包含通联关系分析）"""
        print("🚀 启动APP涉案监控系统全面分析")
        print("=" * 60)
        print(f"📅 分析周期: 最近{days}天")
        print(f"🔄 使用缓存: {'是' if use_cache else '否'}")
        print()

        # 1. 获取数据
        print("📡 获取数据...")
        df = self.get_comprehensive_data(days=days, use_cache=use_cache)
        if df is None or df.empty:
            print("❌ 无法获取数据，分析终止")
            return None

        # 2. 数据概览
        print("\n📊 数据概览")
        print("=" * 60)
        print(
            f"📅 数据时间范围: {df['insert_day'].min().strftime('%Y-%m-%d')} 至 {df['insert_day'].max().strftime('%Y-%m-%d')}")
        print(f"📱 涉案APP数量: {df['final_app_name'].nunique()} 个")
        print(f"📝 案件总数: {len(df)} 起")
        if 'involved_amount' in df.columns:
            print(f"💰 涉案总金额: {df['involved_amount'].sum():,.2f} 元")
            print(f"💸 平均涉案金额: {df['involved_amount'].mean():,.2f} 元")

        # 3. 执行各项分析
        analysis_results = {}

        # 资金分析
        analysis_results['financial'] = self.analyze_financial_impact(df)

        # 新APP检测
        analysis_results['new_apps'] = self.detect_new_apps(df)

        # 诈骗手段分析
        analysis_results['fraud_methods'] = self.analyze_fraud_methods(df)

        # 受害人画像
        analysis_results['victim_profile'] = self.analyze_victim_profile(df)

        # 可疑模式检测（始终使用完整版）
        analysis_results['alerts'] = self.detect_suspicious_patterns(df)

        # 通联关系分析（新增）
        analysis_results['network_analysis'] = self.analyze_network_relationships(df)

        # 4. 生成风险分析报告
        print("\n📊 生成风险分析报告...")
        try:
            report_file = self.create_comprehensive_dashboard(df, analysis_results)
            analysis_results['report_file'] = report_file
        except Exception as e:
            print(f"⚠️ 风险分析报告生成失败: {e}")

        print("\n✅ 全面分析完成！")
        print("=" * 60)

        return analysis_results

    def detect_suspicious_patterns_fast(self, df, display_limit=20):
        """快速可疑模式检测（简化版）- 统一版本"""
        if df is None or df.empty:
            print("❌ 无法检测可疑模式：无数据")
            return None

        print("\n🚨 快速可疑模式检测")
        print("=" * 60)

        alerts = []
        current_time = datetime.now()

        # 1. 检测案件数异常增长的APP
        if 'final_app_name' in df.columns and 'insert_day' in df.columns:
            recent_days = 3
            recent_date = current_time - timedelta(days=recent_days)

            recent_cases = df[df['insert_day'] >= recent_date]
            if not recent_cases.empty:
                recent_app_counts = recent_cases['final_app_name'].value_counts()
                significant_apps = recent_app_counts[recent_app_counts >= 3].head(10)

                for app, recent_count in significant_apps.items():
                    history_start = current_time - timedelta(days=10)
                    history_end = current_time - timedelta(days=recent_days)
                    history_cases = df[(df['insert_day'] >= history_start) &
                                       (df['insert_day'] < history_end) &
                                       (df['final_app_name'] == app)]

                    if len(history_cases) > 0:
                        historical_avg = len(history_cases) / 7 * recent_days
                        if recent_count > historical_avg * 2:
                            alerts.append({
                                'type': '案件数激增',
                                'app_name': app,
                                'severity': '高' if recent_count > historical_avg * 3 else '中',
                                'description': f'{app} 最近{recent_days}天案件数({recent_count})较近7天案件平均数({historical_avg:.1f})增长{recent_count / historical_avg:.1f}倍'
                            })

        # 2. 检测异常高额案件
        if 'involved_amount' in df.columns and 'final_app_name' in df.columns:
            high_amount_threshold = 100000
            high_amount_cases = df[df['involved_amount'] >= high_amount_threshold]

            if not high_amount_cases.empty:
                app_high_amounts = high_amount_cases.groupby('final_app_name').agg({
                    'involved_amount': ['count', 'max']
                }).round(0)
                app_high_amounts.columns = ['high_count', 'max_amount']
                app_high_amounts = app_high_amounts[app_high_amounts['high_count'] >= 2].head(5)

                for app, row in app_high_amounts.iterrows():
                    alerts.append({
                        'type': '异常高额',
                        'app_name': app,
                        'severity': '高' if row['max_amount'] >= 500000 else '中',
                        'description': f'{app} 出现{row["high_count"]}起高额案件，最高{row["max_amount"]:,.0f}元'
                    })

        # 输出结果
        if alerts:
            total_alerts = len(alerts)
            severity_order = {'高': 3, '中': 2, '低': 1}
            alerts.sort(key=lambda x: severity_order.get(x['severity'], 0), reverse=True)

            display_alerts = alerts[:display_limit]

            if total_alerts > display_limit:
                print(f"🚨 发现 {total_alerts} 个可疑模式，显示TOP{display_limit}个:")
            else:
                print(f"🚨 发现 {total_alerts} 个可疑模式:")

            for i, alert in enumerate(display_alerts, 1):
                severity_emoji = {'高': '🔴', '中': '🟡', '低': '🟢'}.get(alert['severity'], '⚪')
                print(f"{i:2d}. [{alert['type']}] {alert['app_name']} {severity_emoji}")
                print(f"    {alert['description']}")
        else:
            print("✅ 未发现明显的可疑模式")

        return alerts
    def close_connection(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            self.is_connected = False
            print("🔌 数据库连接池已关闭")

        # 清理内存缓存
        with self._cache_lock:
            self._memory_cache.clear()


# 使用示例
# 修改主程序入口点
if __name__ == "__main__":
    monitor = AdvancedAppFraudMonitor()

    try:
        # 直接运行完整分析
        print("🚀 开始数据分析...")
        results = monitor.run_comprehensive_analysis(days=30, use_cache=True)

        if results:
            print("\n🎉 分析完成！")

    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        monitor.close_connection()