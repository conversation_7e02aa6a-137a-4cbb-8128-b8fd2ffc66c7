import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime, timedelta
import warnings
import pymysql
import json
import os
from pathlib import Path
from tqdm import tqdm
import seaborn as sns

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False


class AppFraudMonitor:
    """APP涉案数量监控系统"""

    def __init__(self):
        self.connection = None
        self.is_connected = False

        # 配色方案
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'accent': '#F18F01',
            'danger': '#C73E1D',
            'warning': '#F0BE38',
            'success': '#5C946E',
            'neutral': '#8B9DC3',
            'background': '#F8F9FA',
            'grid': '#E9ECEF',
            'text': '#2C3E50'
        }

        # 数据库配置
        self.db_config = {
            'host': '************',
            'port': 57861,
            'user': 'FzUser',
            'password': 'jZ4%fQ3}oI8(jH7)',
            'database': 'antiFraudPlatform',
            'charset': 'utf8mb4'
        }

        # 缓存配置
        self.cache_dir = Path("../cache")
        self.cache_dir.mkdir(exist_ok=True)
        self.cache_file = self.cache_dir / "app_fraud_cache.json"

        # 图表导出配置
        self.export_dir = Path("/Users/<USER>/Documents/测试")
        self.export_dir.mkdir(exist_ok=True)

        # 颜色调色板 - 用于堆叠柱状图
        self.color_palette = [
            '#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#5C946E',
            '#8B9DC3', '#F0BE38', '#7B68EE', '#20B2AA', '#FF6347',
            '#32CD32', '#FFD700', '#FF69B4', '#1E90FF', '#FF4500',
            '#9370DB', '#00CED1', '#FFA500', '#DC143C', '#00FA9A',
            '#4169E1', '#FF1493', '#00BFFF', '#FF6B6B', '#4ECDC4',
            '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'
        ]

    def connect_database(self):
        """连接数据库"""
        try:
            if self.is_connected and self.connection:
                return True

            self.connection = pymysql.connect(**self.db_config)
            self.is_connected = True
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            self.is_connected = False
            return False

    def get_app_data(self, days=30, top_n=30, use_cache=True):
        """获取APP涉案数据"""
        # 检查缓存
        if use_cache and self.cache_file.exists():
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)

                cache_date = cache_data.get('cache_date')
                today = datetime.now().strftime('%Y-%m-%d')

                if cache_date == today:
                    print("📦 使用缓存数据")
                    df = pd.DataFrame(cache_data['data'])
                    # 修复缓存数据的日期格式
                    if not df.empty and 'date_col' in df.columns:
                        df['date_col'] = pd.to_datetime(df['date_col'])

                    # 对缓存数据也应用过滤和标准化
                    print("🔄 对缓存数据应用过滤和标准化...")
                    df = self.filter_invalid_app_names(df)
                    if not df.empty:
                        df = self.normalize_app_names(df)
                        # 重新聚合数据
                        df = df.groupby(['date_col', 'app_name'])['case_count'].sum().reset_index()
                        # 重新筛选TOP N
                        top_apps = df.groupby('app_name')['case_count'].sum().nlargest(top_n).index.tolist()
                        df = df[df['app_name'].isin(top_apps)]

                    return df
            except Exception as e:
                print(f"⚠️ 缓存读取失败: {e}")
                pass

        # 查询数据库
        df = self._query_app_database(days, top_n)

        # 保存缓存
        if use_cache and df is not None and not df.empty:
            try:
                cache_data = {
                    'cache_date': datetime.now().strftime('%Y-%m-%d'),
                    'data': df.to_dict('records')
                }
                with open(self.cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_data, f, ensure_ascii=False, indent=2, default=str)
                print("💾 数据已缓存")
            except Exception:
                pass

        return df

    def filter_invalid_app_names(self, df):
        """过滤无意义的APP名称"""
        if df is None or df.empty:
            return df

        import re

        # 显示过滤前的APP数量
        original_apps = df['app_name'].nunique()
        print(f"🔍 过滤前APP数量: {original_apps}")

        # 创建APP名称的清理副本
        df_work = df.copy()
        df_work['app_name_clean'] = df_work['app_name'].str.strip()

        # 过滤条件
        mask = pd.Series(True, index=df_work.index)

        # 1. 过滤空值和空字符串
        mask &= df_work['app_name_clean'].notna()
        mask &= df_work['app_name_clean'] != ''

        # 2. 过滤纯数字（包括0、1、2等）
        mask &= ~df_work['app_name_clean'].str.match(r'^\d+$', na=False)

        # 3. 过滤问号模式（?、??、???、????等）
        mask &= ~df_work['app_name_clean'].str.match(r'^[?？]+$', na=False)

        # 4. 过滤星号模式（*、**、***等）
        mask &= ~df_work['app_name_clean'].str.match(r'^\*+$', na=False)

        # 5. 过滤井号模式（#、##、###等）
        mask &= ~df_work['app_name_clean'].str.match(r'^#+$', na=False)

        # 6. 过滤点号模式（.、..、...、。。。等）
        mask &= ~df_work['app_name_clean'].str.match(r'^[\.。]+$', na=False)

        # 7. 过滤横线模式（-、--、—、——等）
        mask &= ~df_work['app_name_clean'].str.match(r'^[-—]+$', na=False)

        # 8. 过滤斜线模式（/、//、\、\\等）
        mask &= ~df_work['app_name_clean'].str.match(r'^[/\\]+$', na=False)

        # 9. 过滤无意义词汇（不区分大小写）
        meaningless_patterns = [
            r'^无$', r'^wu$', r'^未知$', r'^unknown$', r'^null$',
            r'^其他$', r'^其它$', r'^不详$', r'^不明$', r'^暂无$', r'^待定$',
            r'^test$', r'^demo$', r'^temp$', r'^临时$', r'^测试$', r'^特殊字符$',
            r'^韩文$', r'^日文$', r'^符号$', r'^特殊符号$', r'^已删除$', r'^"韩文APP"$',
            r'^受害人无法提供$', r'^teams?$', r'^"特殊符号"$', r'^"APP"$', r'^"app"$',
            r'^app$', r'^APP$', r'^韩文APP$', r'^约炮软件$'
        ]
        for pattern in meaningless_patterns:
            mask &= ~df_work['app_name_clean'].str.match(pattern, case=False, na=False)

        # 9.5. 过滤带引号的特殊模式（需要转义引号）
        patterns_to_filter = [
            r'^"韩文APP"$', r'^"特殊符号"$', r'^"APP"$', r'^"app"$',
            r'^韩文APP$', r'^app$', r'^APP$', r'^约炮软件$'
        ]
        for pattern in patterns_to_filter:
            mask &= ~df_work['app_name_clean'].str.contains(pattern, case=False, na=False, regex=True)

        # 10. 过滤纯特殊符号组合（长度1-8，只包含特殊符号）
        # mask &= ~df_work['app_name_clean'].str.match(r'^[^\w\u4e00-\u9fff]{1,8}$', na=False)

        # 11. 过滤长度过短的名称（小于2个字符，除非是知名APP）
        # known_short_apps = ['QQ', 'UC', '58', '12306', '360', 'WPS']
        # # 过滤长度小于等于3的纯英文APP名称（除了已知的短名称APP）
        # short_mask = (df_work['app_name_clean'].str.len() > 3) | df_work['app_name_clean'].str.upper().isin(
        #     [app.upper() for app in known_short_apps])
        # # 特别过滤 "APP" 和 "app"
        # short_mask &= ~df_work['app_name_clean'].str.match(r'^[Aa][Pp][Pp]$', na=False)

        # 12. 过滤混合无意义模式（数字+特殊符号，如：1-、2*、3?等）
        mask &= ~df_work['app_name_clean'].str.match(r'^\d+[^\w\u4e00-\u9fff]+$', na=False)
        mask &= ~df_work['app_name_clean'].str.match(r'^[^\w\u4e00-\u9fff]+\d+$', na=False)

        # 应用过滤条件
        filtered_df = df_work[mask].copy()

        # 移除工作列
        filtered_df = filtered_df.drop('app_name_clean', axis=1)

        # 显示过滤结果
        filtered_apps = filtered_df['app_name'].nunique()
        print(f"✅ 过滤后APP数量: {filtered_apps}")

        if len(filtered_df) < len(df):
            removed_count = len(df) - len(filtered_df)
            print(f"🧹 已过滤 {removed_count} 条无效APP记录")

            # 显示被过滤的APP示例
            removed_apps = set(df['app_name'].unique()) - set(filtered_df['app_name'].unique())
            if removed_apps:
                sample_removed = sorted(list(removed_apps))[:8]  # 显示前8个被过滤的APP
                print(f"🗑️ 被过滤的APP示例: {', '.join(sample_removed)}")

        return filtered_df

    def normalize_app_names(self, df):
        """标准化APP名称 - 处理大小写和相似名称"""
        if df is None or df.empty:
            return df

        # 创建标准化映射
        normalization_map = {
            # 大小写标准化
            'teams': 'Teams',
            'TEAMS': 'Teams',
            'qq': 'QQ',
            'QQ': 'QQ',
            'wechat': 'WeChat',
            'WECHAT': 'WeChat',
            'alipay': 'Alipay',
            'ALIPAY': 'Alipay',
            'taobao': 'Taobao',
            'TAOBAO': 'Taobao',
            # 相似名称合并
            '微信支付': '微信',
            'WeChat Pay': 'WeChat',
            '支付宝钱包': '支付宝',
            'Alipay Wallet': 'Alipay',
            # 英文中文对应
            'WeChat': '微信',
            'Alipay': '支付宝',
            'Taobao': '淘宝',
            'JD': '京东',
            'Meituan': '美团'
        }

        df_normalized = df.copy()

        # 应用标准化映射
        for old_name, new_name in normalization_map.items():
            mask = df_normalized['app_name'].str.lower() == old_name.lower()
            df_normalized.loc[mask, 'app_name'] = new_name

        # 统计标准化效果
        original_count = df['app_name'].nunique()
        normalized_count = df_normalized['app_name'].nunique()

        if original_count != normalized_count:
            merged_count = original_count - normalized_count
            print(f"🔄 APP名称标准化: 合并了 {merged_count} 个相似名称")

        return df_normalized

    def _query_app_database(self, days=30, top_n=30):
        """查询APP数据库"""
        if not self.is_connected and not self.connect_database():
            return None

        try:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

            # 查询APP名称和对应的案情数量，按日期分组
            query = f"""
            SELECT
                DATE(insert_day) as date_col,
                app_name,
                COUNT(*) as case_count
            FROM anti_fraud_case_new
            WHERE DATE(insert_day) BETWEEN '{start_date}' AND '{end_date}'
              AND app_name IS NOT NULL
              AND app_name != ''
              AND app_name != 'NULL'
            GROUP BY DATE(insert_day), app_name
            ORDER BY DATE(insert_day), case_count DESC
            """

            df = pd.read_sql(query, self.connection)

            if not df.empty:
                df['date_col'] = pd.to_datetime(df['date_col'])

                # 过滤无意义的APP名称
                df = self.filter_invalid_app_names(df)

                if df.empty:
                    print("⚠️ 过滤后无有效数据")
                    return df

                # 标准化APP名称
                df = self.normalize_app_names(df)

                # 重新聚合数据（因为标准化可能合并了一些APP）
                df = df.groupby(['date_col', 'app_name'])['case_count'].sum().reset_index()

                # 获取TOP N APP（按总案件数排序）
                top_apps = df.groupby('app_name')['case_count'].sum().nlargest(top_n).index.tolist()
                df = df[df['app_name'].isin(top_apps)]

                print(f"✅ 查询成功，共 {len(df)} 条记录，TOP{top_n} APP数据")
                print(f"📊 数据范围: {start_date} 至 {end_date}")
                print(f"📈 涉及APP数量: {df['app_name'].nunique()} 个")
            else:
                print("⚠️ 查询结果为空")

            return df

        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return None

    def prepare_chart_data(self, df):
        """准备图表数据 - 转换为透视表格式（横坐标为APP，按日期堆叠）"""
        if df is None or df.empty:
            return None

        # 创建透视表：APP名称为列，日期为行，案件数量为值
        pivot_df = df.pivot_table(
            index='date_col',
            columns='app_name',
            values='case_count',
            fill_value=0
        )

        # 按总案件数排序列（APP）
        column_totals = pivot_df.sum().sort_values(ascending=False)
        pivot_df = pivot_df[column_totals.index]

        return pivot_df

    def prepare_stacked_data(self, df):
        """准备堆叠柱状图数据 - 横坐标为APP，按日期堆叠"""
        if df is None or df.empty:
            return None, None

        # 创建透视表：日期为列，APP名称为行，案件数量为值
        pivot_df = df.pivot_table(
            index='app_name',
            columns='date_col',
            values='case_count',
            fill_value=0
        )

        # 按总案件数排序行（APP）
        row_totals = pivot_df.sum(axis=1).sort_values(ascending=False)
        pivot_df = pivot_df.loc[row_totals.index]

        # 按日期排序列
        pivot_df = pivot_df.sort_index(axis=1)

        return pivot_df, row_totals

    def create_trend_line_chart(self, df, save_chart=False):
        """创建多系列折线图 - 展示TOP APP的日新增趋势"""
        if df is None or df.empty:
            print("⚠️ 无数据可绘制图表")
            return

        # 准备数据
        pivot_df = self.prepare_chart_data(df)
        if pivot_df is None:
            print("❌ 数据准备失败")
            return

        # 只显示TOP10 APP以保持图表清晰
        top_10_apps = pivot_df.sum().nlargest(10).index
        pivot_df_top10 = pivot_df[top_10_apps]

        # 创建图表
        fig, ax = plt.subplots(figsize=(20, 12))
        fig.patch.set_facecolor(self.colors['background'])

        # 绘制折线图
        for i, app_name in enumerate(pivot_df_top10.columns):
            color = self.color_palette[i % len(self.color_palette)]

            # 绘制折线
            line = ax.plot(
                pivot_df_top10.index,
                pivot_df_top10[app_name],
                marker='o',
                linewidth=3,
                markersize=8,
                label=app_name,
                color=color,
                alpha=0.8
            )

            # 在数据点上添加数值标签（只在值大于0时显示）
            for x, y in zip(pivot_df_top10.index, pivot_df_top10[app_name]):
                if y > 0:
                    ax.annotate(f'{int(y)}',
                               (x, y),
                               textcoords="offset points",
                               xytext=(0,10),
                               ha='center',
                               fontsize=9,
                               color=color,
                               fontweight='bold')

        # 美化图表
        ax.set_title('TOP10 APP涉案数量趋势图)',
                     fontsize=22, fontweight='bold', pad=30, color=self.colors['text'])
        ax.set_xlabel('日期', fontsize=16, color=self.colors['text'])
        ax.set_ylabel('案件数量', fontsize=16, color=self.colors['text'])

        # 设置x轴日期格式
        import matplotlib.dates as mdates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))

        # 旋转日期标签
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
        ax.tick_params(axis='both', labelsize=12)

        # 网格
        ax.grid(True, alpha=0.3, color=self.colors['grid'], linewidth=0.8)
        ax.set_facecolor(self.colors['background'])

        # 图例设置
        legend = ax.legend(
            bbox_to_anchor=(1.05, 1),
            loc='upper left',
            fontsize=12,
            frameon=True,
            fancybox=True,
            shadow=True,
            title='APP名称'
        )
        legend.get_frame().set_facecolor('white')
        legend.get_frame().set_alpha(0.95)

        # 设置Y轴从0开始
        ax.set_ylim(bottom=0)

        # 移除边框
        for spine in ax.spines.values():
            spine.set_visible(False)

        plt.tight_layout()

        # 保存图表
        if save_chart:
            self.export_chart("APP涉案统计_趋势图")

        plt.show()

        # 显示统计信息
        print(f"\n📈 趋势图统计:")
        print(f"  - 显示APP数量: {len(top_10_apps)} 个")
        print(f"  - 统计日期数: {len(pivot_df_top10)} 天")
        print(f"  - TOP3 APP: {', '.join(top_10_apps[:3].tolist())}")

        # 显示每个APP的总案件数
        app_totals = pivot_df_top10.sum().sort_values(ascending=False)
        print(f"  - APP案件总数:")
        for app, total in app_totals.head(5).items():
            print(f"    {app}: {int(total)} 起")

    def create_stacked_bar_chart(self, df, save_chart=False):
        """创建堆叠柱状图 - 横坐标为APP名称，按日期堆叠"""
        if df is None or df.empty:
            print("⚠️ 无数据可绘制图表")
            return

        # 准备数据
        pivot_df, app_totals = self.prepare_stacked_data(df)
        if pivot_df is None:
            print("❌ 数据准备失败")
            return

        # 只显示TOP20的APP
        top_20_apps = app_totals.head(20).index
        pivot_df = pivot_df.loc[top_20_apps]

        # 创建图表
        fig, ax = plt.subplots(figsize=(24, 12))
        fig.patch.set_facecolor(self.colors['background'])

        # 准备堆叠数据
        app_names = pivot_df.index.tolist()
        dates = pivot_df.columns.tolist()

        # 为每个日期分配颜色
        date_colors = {}
        for i, date in enumerate(dates):
            date_colors[date] = self.color_palette[i % len(self.color_palette)]

        # 绘制堆叠柱状图
        bottom = np.zeros(len(app_names))

        for date in dates:
            values = pivot_df[date].values

            bars = ax.bar(
                app_names,
                values,
                bottom=bottom,
                label=date.strftime('%m-%d'),
                color=date_colors[date],
                alpha=0.8,
                edgecolor='white',
                linewidth=0.5
            )

            bottom += values

        # 美化图表
        ax.set_title('APP涉案数量统计 - TOP20堆叠柱状图)',
                     fontsize=20, fontweight='bold', pad=20, color=self.colors['text'])
        ax.set_xlabel('APP名称', fontsize=16, color=self.colors['text'])
        ax.set_ylabel('案件数量', fontsize=16, color=self.colors['text'])

        # 设置x轴标签
        ax.tick_params(axis='x', rotation=45, labelsize=10)
        ax.tick_params(axis='y', labelsize=12)

        # 网格
        ax.grid(True, alpha=0.3, color=self.colors['grid'], linewidth=0.8, axis='y')
        ax.set_facecolor(self.colors['background'])

        # 图例设置 - 显示日期
        legend = ax.legend(
            bbox_to_anchor=(1.05, 1),
            loc='upper left',
            fontsize=10,
            ncol=3,
            frameon=True,
            fancybox=True,
            shadow=True,
            title='日期'
        )
        legend.get_frame().set_facecolor('white')
        legend.get_frame().set_alpha(0.95)

        # 移除边框
        for spine in ax.spines.values():
            spine.set_visible(False)

        plt.tight_layout()

        # 保存图表
        if save_chart:
            self.export_chart("APP涉案统计_堆叠柱状图")

        plt.show()

        # 显示统计信息
        print(f"\n📊 图表统计:")
        print(f"  - 显示APP数量: {len(app_names)} 个")
        print(f"  - 统计日期数: {len(dates)} 天")
        print(f"  - 总案件数: {pivot_df.values.sum()} 起")
        print(f"  - TOP3 APP: {', '.join(app_totals.head(3).index.tolist())}")

    def create_horizontal_bar_chart(self, df):
        """创建水平条形图 - 显示TOP50总量"""
        if df is None or df.empty:
            print("⚠️ 无数据可绘制图表")
            return

        # 计算每个APP的总案件数
        app_totals = df.groupby('app_name')['case_count'].sum().sort_values(ascending=True)

        # 获取实际的APP数量
        actual_count = len(app_totals)
        display_count = min(actual_count, 50)  # 最多显示50个

        # 创建图表 - 根据APP数量调整高度
        height = max(16, display_count * 0.4)  # 每个APP至少0.4英寸高度
        fig, ax = plt.subplots(figsize=(14, height))
        fig.patch.set_facecolor(self.colors['background'])

        # 绘制水平条形图
        bars = ax.barh(
            app_totals.index,
            app_totals.values,
            color=self.colors['primary'],
            alpha=0.8,
            edgecolor='white',
            linewidth=0.5
        )

        # 在条形图上添加数值标签
        for bar, value in zip(bars, app_totals.values):
            ax.text(bar.get_width() + 0.1, bar.get_y() + bar.get_height() / 2,
                    f'{int(value)}', ha='left', va='center', fontsize=10)

        # 美化图表
        ax.set_title(f'APP涉案数量排名 - TOP{display_count})',
                     fontsize=20, fontweight='bold', pad=20, color=self.colors['text'])
        ax.set_xlabel('案件数量', fontsize=14, color=self.colors['text'])
        ax.set_ylabel('APP名称', fontsize=14, color=self.colors['text'])

        # 网格
        ax.grid(True, alpha=0.3, color=self.colors['grid'], linewidth=0.8, axis='x')
        ax.set_facecolor(self.colors['background'])

        # 移除边框
        for spine in ax.spines.values():
            spine.set_visible(False)

        plt.tight_layout()
        plt.show()

    def show_statistics(self, df):
        """显示统计信息"""
        if df is None or df.empty:
            print("❌ 无数据可统计")
            return

        print("\n📊 APP涉案统计信息")
        print("=" * 60)

        # 基本统计
        total_cases = df['case_count'].sum()
        total_apps = df['app_name'].nunique()
        date_range = df['date_col'].nunique()

        print(f"📈 统计周期: {date_range} 天")
        print(f"📱 涉及APP: {total_apps} 个")
        print(f"📊 案件总数: {total_cases} 起")
        print(f"📊 日均案件: {total_cases / date_range:.1f} 起")

        # TOP100 APP
        top_apps = df.groupby('app_name')['case_count'].sum().nlargest(100)
        print(f"\n🏆 TOP100 APP:")
        print("-" * 40)
        for i, (app, count) in enumerate(top_apps.items(), 1):
            print(f"{i:2d}. {app}: {count} 起")

        print("=" * 60)

    def export_chart(self, filename=None):
        """导出图表到文件"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"APP涉案统计_{timestamp}.png"
        else:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{filename}_{timestamp}.png"

        filepath = self.export_dir / filename

        try:
            plt.savefig(filepath, dpi=300, bbox_inches='tight',
                        facecolor='white', edgecolor='none')
            print(f"📁 图表已保存到: {filepath}")
            return True
        except Exception as e:
            print(f"❌ 图表保存失败: {e}")
            return False

    def clear_cache(self):
        """清除缓存"""
        try:
            if self.cache_file.exists():
                self.cache_file.unlink()
                print("🗑️ 缓存已清除，下次查询将重新获取数据并应用过滤")
            else:
                print("ℹ️ 缓存文件不存在")
        except Exception as e:
            print(f"❌ 清除缓存失败: {e}")

    def close_connection(self):
        """关闭数据库连接"""
        if self.is_connected and self.connection:
            try:
                if self.connection.open:
                    self.connection.close()
                    print("🔒 数据库连接已关闭")
            except Exception:
                pass
            finally:
                self.is_connected = False
                self.connection = None

    def run_interactive_mode(self):
        """交互式运行模式"""
        print("\n🎯 APP涉案数量监控系统")
        print("=" * 60)

        while True:
            print("\n请选择操作：")
            print("1. 🐮 显示新图表")
            print("2. 📊 显示堆叠柱状图")
            print("3. 📈 显示排名条形图 (TOP50)")
            print("4. 📋 显示统计信息")
            print("5. 💾 保存堆叠柱状图")
            print("6. 🗑️ 清除缓存")
            print("7. ❌ 退出系统")

            try:
                choice = input("\n请输入选项编号: ").strip()

                if choice == '1':
                    print("\n� 正在生成趋势图...")
                    df = self.get_app_data(days=30, top_n=10)
                    if df is not None and not df.empty:
                        self.create_trend_line_chart(df, save_chart=False)
                    else:
                        print("❌ 无法获取数据")

                elif choice == '2':
                    print("\n📊 正在生成堆叠柱状图...")
                    df = self.get_app_data(days=30, top_n=20)
                    if df is not None and not df.empty:
                        self.create_stacked_bar_chart(df, save_chart=False)
                    else:
                        print("❌ 无法获取数据")

                elif choice == '3':
                    print("\n📈 正在生成排名条形图(TOP50)...")
                    df = self.get_app_data(days=30, top_n=50)
                    if df is not None and not df.empty:
                        self.create_horizontal_bar_chart(df)
                    else:
                        print("❌ 无法获取数据")

                elif choice == '4':
                    print("\n📋 正在获取统计信息...")
                    df = self.get_app_data(days=30, top_n=100)
                    self.show_statistics(df)

                elif choice == '5':
                    print("\n💾 正在保存堆叠柱状图...")
                    df = self.get_app_data(days=30, top_n=20)
                    if df is not None and not df.empty:
                        self.create_stacked_bar_chart(df, save_chart=True)
                    else:
                        print("❌ 无法获取数据")

                elif choice == '6':
                    self.clear_cache()

                elif choice == '7':
                    print("👋 系统已退出")
                    break

                else:
                    print("❌ 无效选项，请重新输入")

            except KeyboardInterrupt:
                print("\n👋 用户中断，系统退出")
                break
            except Exception as e:
                print(f"❌ 操作异常: {e}")

    def __del__(self):
        """析构函数，确保资源清理"""
        self.close_connection()


def main():
    """主函数"""
    monitor = AppFraudMonitor()

    try:
        # 启动交互模式
        monitor.run_interactive_mode()
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序运行异常: {e}")
    finally:
        # 确保资源清理
        monitor.close_connection()
        print("🔒 资源已清理")


if __name__ == "__main__":
    main()