#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试趋势图功能和APP名称过滤
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime, timedelta
import re

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

def filter_invalid_app_names(df):
    """过滤无意义的APP名称"""
    if df is None or df.empty:
        return df
        
    print(f"📊 过滤前APP数量: {df['app_name'].nunique()}")
    
    # 定义无意义的APP名称模式
    invalid_patterns = [
        '无', '-', '—', '/', '\\', '未知', 'unknown', 'null', 'NULL',
        '1', '2', '3', '4', '5', '6', '7', '8', '9', '0',
        '其他', '其它', '不详', '不明', '暂无', '待定',
        '***', '###', '...', '。。。', '???',
        'test', 'Test', 'TEST', 'demo', 'Demo', 'DEMO'
    ]
    
    # 过滤条件
    mask = True
    
    # 1. 过滤空值和空字符串
    mask &= df['app_name'].notna()
    mask &= df['app_name'].str.strip() != ''
    
    # 2. 过滤无意义的模式
    for pattern in invalid_patterns:
        mask &= df['app_name'].str.strip() != pattern
    
    # 3. 过滤纯数字
    mask &= ~df['app_name'].str.strip().str.isdigit()
    
    # 4. 过滤纯特殊符号（长度小于等于3且包含特殊符号）
    special_chars_pattern = r'^[^\w\u4e00-\u9fff]{1,3}$'
    mask &= ~df['app_name'].str.match(special_chars_pattern, na=False)
    
    # 5. 过滤长度过短的名称（小于2个字符，除非是知名APP）
    known_short_apps = ['QQ', 'UC', '58', '12306', '360', 'WPS']
    short_mask = (df['app_name'].str.len() >= 2) | df['app_name'].isin(known_short_apps)
    mask &= short_mask
    
    filtered_df = df[mask].copy()
    
    print(f"🧹 过滤后APP数量: {filtered_df['app_name'].nunique()}")
    if len(filtered_df) < len(df):
        removed_count = len(df) - len(filtered_df)
        print(f"🗑️ 已过滤 {removed_count} 条无效APP记录")
        
    return filtered_df

def create_sample_data():
    """创建示例数据，包含一些无效的APP名称"""
    # 生成14天的日期
    dates = [datetime.now() - timedelta(days=i) for i in range(13, -1, -1)]
    
    # 包含有效和无效的APP名称
    valid_apps = [
        '微信', '支付宝', '抖音', '淘宝', '京东', 
        '美团', '拼多多', '百度', '腾讯视频', '爱奇艺',
        'QQ', 'UC浏览器', '网易云音乐', '知乎', '小红书'
    ]
    
    invalid_apps = [
        '无', '-', '1', '2', '***', '未知', 'null', 
        '其他', '...', '???', 'test', '###'
    ]
    
    all_apps = valid_apps + invalid_apps
    
    # 生成随机数据
    data = []
    for date in dates:
        for app in all_apps:
            # 有效APP有更高的案件数量
            if app in valid_apps:
                if app in ['微信', '支付宝', '抖音']:
                    case_count = np.random.randint(5, 25)  # 高风险APP
                elif app in ['淘宝', '京东', '美团']:
                    case_count = np.random.randint(3, 15)  # 中风险APP
                else:
                    case_count = np.random.randint(1, 10)  # 低风险APP
            else:
                # 无效APP也有少量案件
                case_count = np.random.randint(0, 3)
            
            if case_count > 0:  # 只记录有案件的数据
                data.append({
                    'date_col': date,
                    'app_name': app,
                    'case_count': case_count
                })
    
    return pd.DataFrame(data)

def prepare_chart_data(df):
    """准备图表数据"""
    if df is None or df.empty:
        return None

    # 创建透视表：APP名称为列，日期为行，案件数量为值
    pivot_df = df.pivot_table(
        index='date_col',
        columns='app_name',
        values='case_count',
        fill_value=0
    )

    # 按总案件数排序列（APP）
    column_totals = pivot_df.sum().sort_values(ascending=False)
    pivot_df = pivot_df[column_totals.index]

    return pivot_df

def create_trend_line_chart(df):
    """创建多系列折线图 - 展示TOP APP的日新增趋势"""
    if df is None or df.empty:
        print("⚠️ 无数据可绘制图表")
        return

    # 准备数据
    pivot_df = prepare_chart_data(df)
    if pivot_df is None:
        print("❌ 数据准备失败")
        return

    # 只显示TOP10 APP以保持图表清晰
    top_10_apps = pivot_df.sum().nlargest(10).index
    pivot_df_top10 = pivot_df[top_10_apps]

    # 颜色调色板
    color_palette = [
        '#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#5C946E',
        '#8B9DC3', '#F0BE38', '#7B68EE', '#20B2AA', '#FF6347'
    ]

    # 创建图表
    fig, ax = plt.subplots(figsize=(16, 10))
    fig.patch.set_facecolor('#F8F9FA')

    # 绘制折线图
    for i, app_name in enumerate(pivot_df_top10.columns):
        color = color_palette[i % len(color_palette)]
        
        # 绘制折线
        ax.plot(
            pivot_df_top10.index,
            pivot_df_top10[app_name],
            marker='o',
            linewidth=3,
            markersize=8,
            label=app_name,
            color=color,
            alpha=0.8
        )
        
        # 在数据点上添加数值标签（只在值大于0时显示）
        for x, y in zip(pivot_df_top10.index, pivot_df_top10[app_name]):
            if y > 0:
                ax.annotate(f'{int(y)}', 
                           (x, y), 
                           textcoords="offset points", 
                           xytext=(0,10), 
                           ha='center',
                           fontsize=9,
                           color=color,
                           fontweight='bold')

    # 美化图表
    ax.set_title('TOP10 APP涉案数量趋势图\n(清晰展示每日新增案件趋势)',
                 fontsize=18, fontweight='bold', pad=20, color='#2C3E50')
    ax.set_xlabel('日期', fontsize=14, color='#2C3E50')
    ax.set_ylabel('案件数量', fontsize=14, color='#2C3E50')

    # 设置x轴日期格式
    import matplotlib.dates as mdates
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
    ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))
    
    # 旋转日期标签
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    ax.tick_params(axis='both', labelsize=12)

    # 网格
    ax.grid(True, alpha=0.3, color='#E9ECEF', linewidth=0.8)
    ax.set_facecolor('#F8F9FA')

    # 图例设置
    legend = ax.legend(
        bbox_to_anchor=(1.05, 1),
        loc='upper left',
        fontsize=11,
        frameon=True,
        fancybox=True,
        shadow=True,
        title='APP名称'
    )
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_alpha(0.95)

    # 设置Y轴从0开始
    ax.set_ylim(bottom=0)

    # 移除边框
    for spine in ax.spines.values():
        spine.set_visible(False)

    plt.tight_layout()
    plt.show()

    # 显示统计信息
    print(f"\n📈 趋势图统计:")
    print(f"  - 显示APP数量: {len(top_10_apps)} 个")
    print(f"  - 统计日期数: {len(pivot_df_top10)} 天")
    print(f"  - TOP3 APP: {', '.join(top_10_apps[:3].tolist())}")
    
    # 显示每个APP的总案件数
    app_totals = pivot_df_top10.sum().sort_values(ascending=False)
    print(f"  - APP案件总数:")
    for app, total in app_totals.head(5).items():
        print(f"    {app}: {int(total)} 起")

def main():
    """主函数"""
    print("📈 APP涉案数量趋势图测试")
    print("=" * 50)
    
    # 创建示例数据
    print("📊 正在生成示例数据...")
    df = create_sample_data()
    print(f"✅ 原始数据生成完成，共 {len(df)} 条记录")
    
    # 过滤无效APP名称
    print("\n🧹 正在过滤无效APP名称...")
    df_filtered = filter_invalid_app_names(df)
    
    # 创建趋势图
    print("\n📈 正在生成趋势图...")
    create_trend_line_chart(df_filtered)

if __name__ == "__main__":
    main()
