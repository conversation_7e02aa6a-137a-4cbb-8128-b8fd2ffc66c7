#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试堆叠柱状图功能
横坐标：APP名称，按日期堆叠
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime, timedelta

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

def create_sample_data():
    """创建示例数据"""
    # 生成7天的日期
    dates = [datetime.now() - timedelta(days=i) for i in range(6, -1, -1)]
    
    # TOP10 APP名称
    apps = [
        '微信', '支付宝', '抖音', '淘宝', '京东', 
        '美团', '拼多多', '百度', '腾讯视频', '爱奇艺'
    ]
    
    # 生成随机数据
    data = []
    for date in dates:
        for app in apps:
            # 随机生成案件数量
            if app in ['微信', '支付宝', '抖音']:
                case_count = np.random.randint(10, 50)  # 高风险APP
            elif app in ['淘宝', '京东', '美团']:
                case_count = np.random.randint(5, 30)   # 中风险APP
            else:
                case_count = np.random.randint(1, 20)   # 低风险APP
            
            data.append({
                'date_col': date,
                'app_name': app,
                'case_count': case_count
            })
    
    return pd.DataFrame(data)

def prepare_stacked_data(df):
    """准备堆叠柱状图数据 - 横坐标为APP，按日期堆叠"""
    if df is None or df.empty:
        return None, None

    # 创建透视表：日期为列，APP名称为行，案件数量为值
    pivot_df = df.pivot_table(
        index='app_name',
        columns='date_col',
        values='case_count',
        fill_value=0
    )

    # 按总案件数排序行（APP）
    row_totals = pivot_df.sum(axis=1).sort_values(ascending=False)
    pivot_df = pivot_df.loc[row_totals.index]

    # 按日期排序列
    pivot_df = pivot_df.sort_index(axis=1)

    return pivot_df, row_totals

def create_stacked_bar_chart(df):
    """创建堆叠柱状图 - 横坐标为APP名称，按日期堆叠"""
    if df is None or df.empty:
        print("⚠️ 无数据可绘制图表")
        return

    # 准备数据
    pivot_df, app_totals = prepare_stacked_data(df)
    if pivot_df is None:
        print("❌ 数据准备失败")
        return

    # 颜色调色板
    color_palette = [
        '#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#5C946E',
        '#8B9DC3', '#F0BE38', '#7B68EE', '#20B2AA', '#FF6347'
    ]

    # 创建图表
    fig, ax = plt.subplots(figsize=(16, 10))
    fig.patch.set_facecolor('#F8F9FA')

    # 准备堆叠数据
    app_names = pivot_df.index.tolist()
    dates = pivot_df.columns.tolist()
    
    # 为每个日期分配颜色
    date_colors = {}
    for i, date in enumerate(dates):
        date_colors[date] = color_palette[i % len(color_palette)]

    # 绘制堆叠柱状图
    bottom = np.zeros(len(app_names))
    
    for date in dates:
        values = pivot_df[date].values
        
        ax.bar(
            app_names,
            values,
            bottom=bottom,
            label=date.strftime('%m-%d'),
            color=date_colors[date],
            alpha=0.8,
            edgecolor='white',
            linewidth=0.5
        )
        
        bottom += values

    # 美化图表
    ax.set_title('APP涉案数量统计 - 堆叠柱状图\n(横坐标：APP名称，按日期堆叠)',
                 fontsize=18, fontweight='bold', pad=20, color='#2C3E50')
    ax.set_xlabel('APP名称', fontsize=14, color='#2C3E50')
    ax.set_ylabel('案件数量', fontsize=14, color='#2C3E50')

    # 设置x轴标签
    ax.tick_params(axis='x', rotation=45, labelsize=12)
    ax.tick_params(axis='y', labelsize=12)

    # 网格
    ax.grid(True, alpha=0.3, color='#E9ECEF', linewidth=0.8, axis='y')
    ax.set_facecolor('#F8F9FA')

    # 图例设置
    legend = ax.legend(
        bbox_to_anchor=(1.05, 1),
        loc='upper left',
        fontsize=10,
        frameon=True,
        fancybox=True,
        shadow=True,
        title='日期'
    )
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_alpha(0.95)

    # 移除边框
    for spine in ax.spines.values():
        spine.set_visible(False)

    plt.tight_layout()
    plt.show()

    # 显示统计信息
    print(f"\n📊 图表统计:")
    print(f"  - 显示APP数量: {len(app_names)} 个")
    print(f"  - 统计日期数: {len(dates)} 天")
    print(f"  - 总案件数: {pivot_df.values.sum()} 起")
    print(f"  - TOP3 APP: {', '.join(app_totals.head(3).index.tolist())}")

def main():
    """主函数"""
    print("📊 APP涉案数量堆叠柱状图测试")
    print("=" * 50)
    
    # 创建示例数据
    print("📊 正在生成示例数据...")
    df = create_sample_data()
    
    print(f"✅ 数据生成完成，共 {len(df)} 条记录")
    
    # 创建堆叠柱状图
    print("📊 正在生成堆叠柱状图...")
    create_stacked_bar_chart(df)

if __name__ == "__main__":
    main()
