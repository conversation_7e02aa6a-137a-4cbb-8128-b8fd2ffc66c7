#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试提示词修复效果
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.idea/Ready/test/50万趋势'))

def test_prompt_fix():
    """测试提示词修复效果"""
    print("🧪 测试提示词修复效果")
    print("=" * 60)
    
    try:
        from prompt_manager import prompt_manager
        
        # 模拟数据 - 移动占比最高的情况
        test_data = {
            'start_date': '2024-01-01',
            'end_date': '2024-01-30',
            'days': 30,
            'total_cases': 1000,
            'daily_avg': 33.3,
            'current_cases': 35,
            'telecom_total': 297,  # 29.7%
            'unicom_total': 184,   # 18.4%
            'mobile_total': 519,   # 51.9%
            'telecom_ratio': 0.297,
            'unicom_ratio': 0.184,
            'mobile_ratio': 0.519,
            'recent_7_days': '[30, 32, 28, 35, 40, 33, 35]',
            'recent_7_avg': 33.3,
            'vs_prev_7_days': 0.05,
            'current_week_total': 233,
            'last_week_total': 221,
            'week_change': 0.054,
            'current_month_total': 1000,
            'last_month_same_period_total': 950,
            'month_change': 0.053,
            'telecom_recent_7_total': 69,
            'telecom_prev_7_total': 65,
            'telecom_current_week_total': 69,
            'telecom_last_week_total': 65
        }
        
        # 生成提示词
        prompt = prompt_manager.format_prompt('trend_analysis', **test_data)
        
        print("📝 生成的提示词片段（运营商分布部分）：")
        print("-" * 60)
        
        # 提取运营商分布相关的部分
        lines = prompt.split('\n')
        in_operator_section = False
        for line in lines:
            if '**运营商分布**：' in line:
                in_operator_section = True
            elif in_operator_section and line.strip():
                if line.startswith('      **') and '运营商分布' not in line:
                    break
                print(line)
            elif in_operator_section and not line.strip():
                print(line)
        
        print("\n" + "-" * 60)
        print("🔍 关键检查点：")
        print(f"✅ 移动案件数：519起（51.9%）")
        print(f"✅ 电信案件数：297起（29.7%）")
        print(f"✅ 联通案件数：184起（18.4%）")
        print(f"✅ 移动占比最高：51.9% > 29.7% > 18.4%")
        
        print("\n📋 修复内容：")
        print("1. ✅ 修改了分析要求，明确要求客观分析占比")
        print("2. ✅ 添加了重要提醒，强调正确识别最高占比")
        print("3. ✅ 删除了可能误导AI的表述")
        
        print("\n🎯 预期效果：")
        print("AI应该正确识别移动用户案件占比最高（51.9%），")
        print("而不是错误地说电信占比最高。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_prompt_fix()
