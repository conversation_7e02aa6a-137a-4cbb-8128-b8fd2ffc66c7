"""
缺失的 analyze_network_relationships 方法
请将此方法添加到您的 AdvancedAppFraudMonitor 类中
"""

def analyze_network_relationships(self, df):
    """分析通联关系和网络模式"""
    if df is None or df.empty:
        print("❌ 无法进行通联关系分析：无数据")
        return None

    print("\n🕸️ 通联关系分析")
    print("=" * 60)

    network_results = {}

    try:
        # 1. 分析共同受害人（基于手机号）
        if 'suspect_phone_number' in df.columns and 'final_app_name' in df.columns:
            phone_app_analysis = self._analyze_phone_app_relationships(df)
            if phone_app_analysis:
                network_results['phone_app_relationships'] = phone_app_analysis

        # 2. 分析跨APP受害人模式
        if 'victim_app_account' in df.columns and 'final_app_name' in df.columns:
            cross_app_analysis = self._analyze_cross_app_victims(df)
            if cross_app_analysis:
                network_results['cross_app_victims'] = cross_app_analysis

        # 3. 分析可疑账号集群
        if 'suspect_account_number' in df.columns:
            account_cluster_analysis = self._analyze_account_clusters(df)
            if account_cluster_analysis:
                network_results['account_clusters'] = account_cluster_analysis

        # 4. 分析时间关联模式
        if 'occurrence_time' in df.columns:
            temporal_analysis = self._analyze_temporal_patterns(df)
            if temporal_analysis:
                network_results['temporal_patterns'] = temporal_analysis

        print(f"✅ 通联关系分析完成，发现 {len(network_results)} 类关联模式")

    except Exception as e:
        print(f"⚠️ 通联关系分析部分失败: {e}")

    return network_results

def _analyze_phone_app_relationships(self, df):
    """分析手机号与APP的关联关系"""
    try:
        phone_app_data = df.dropna(subset=['suspect_phone_number', 'final_app_name'])
        if phone_app_data.empty:
            return None

        # 统计每个手机号涉及的APP数量
        phone_app_counts = phone_app_data.groupby('suspect_phone_number')['final_app_name'].nunique()
        multi_app_phones = phone_app_counts[phone_app_counts > 1]

        if not multi_app_phones.empty:
            print(f"📱 发现 {len(multi_app_phones)} 个手机号涉及多个APP:")
            for phone, app_count in multi_app_phones.head(10).items():
                apps = phone_app_data[phone_app_data['suspect_phone_number'] == phone]['final_app_name'].unique()
                print(f"  {phone}: {app_count}个APP ({', '.join(apps[:3])}{'...' if len(apps) > 3 else ''})")

            return {
                'multi_app_phones_count': len(multi_app_phones),
                'max_apps_per_phone': multi_app_phones.max(),
                'avg_apps_per_phone': multi_app_phones.mean()
            }
    except Exception as e:
        print(f"⚠️ 手机号APP关联分析失败: {e}")
    return None

def _analyze_cross_app_victims(self, df):
    """分析跨APP受害人模式"""
    try:
        # 这里可以基于受害人的其他标识符进行分析
        # 由于数据隐私，我们使用案件时间和金额的相似性来推断
        if 'involved_amount' in df.columns and 'occurrence_time' in df.columns:
            # 寻找相似金额和时间的案件
            similar_cases = []
            amount_tolerance = 0.05  # 5%的金额容差
            time_tolerance = timedelta(hours=24)  # 24小时时间容差

            df_clean = df.dropna(subset=['involved_amount', 'occurrence_time', 'final_app_name'])
            
            for i, row1 in df_clean.iterrows():
                similar_count = 0
                for j, row2 in df_clean.iterrows():
                    if i != j and row1['final_app_name'] != row2['final_app_name']:
                        # 检查金额相似性
                        amount_diff = abs(row1['involved_amount'] - row2['involved_amount']) / max(row1['involved_amount'], 1)
                        # 检查时间相似性
                        time_diff = abs(row1['occurrence_time'] - row2['occurrence_time'])
                        
                        if amount_diff <= amount_tolerance and time_diff <= time_tolerance:
                            similar_count += 1
                
                if similar_count > 0:
                    similar_cases.append({
                        'app': row1['final_app_name'],
                        'amount': row1['involved_amount'],
                        'time': row1['occurrence_time'],
                        'similar_cases': similar_count
                    })

            if similar_cases:
                print(f"🔗 发现 {len(similar_cases)} 个可能的跨APP受害模式")
                return {
                    'potential_cross_app_cases': len(similar_cases),
                    'top_patterns': sorted(similar_cases, key=lambda x: x['similar_cases'], reverse=True)[:5]
                }
    except Exception as e:
        print(f"⚠️ 跨APP受害人分析失败: {e}")
    return None

def _analyze_account_clusters(self, df):
    """分析可疑账号集群"""
    try:
        if 'suspect_account_number' in df.columns:
            account_data = df.dropna(subset=['suspect_account_number'])
            if not account_data.empty:
                # 统计账号出现频次
                account_counts = account_data['suspect_account_number'].value_counts()
                frequent_accounts = account_counts[account_counts > 1]
                
                if not frequent_accounts.empty:
                    print(f"💳 发现 {len(frequent_accounts)} 个重复出现的可疑账号")
                    return {
                        'frequent_accounts_count': len(frequent_accounts),
                        'max_cases_per_account': frequent_accounts.max(),
                        'top_accounts': frequent_accounts.head(5).to_dict()
                    }
    except Exception as e:
        print(f"⚠️ 账号集群分析失败: {e}")
    return None

def _analyze_temporal_patterns(self, df):
    """分析时间关联模式"""
    try:
        if 'occurrence_time' in df.columns:
            time_data = df.dropna(subset=['occurrence_time'])
            if not time_data.empty:
                # 分析案件发生的时间模式
                time_data['hour'] = time_data['occurrence_time'].dt.hour
                time_data['day_of_week'] = time_data['occurrence_time'].dt.dayofweek
                
                # 高发时段分析
                hour_counts = time_data['hour'].value_counts().sort_index()
                peak_hours = hour_counts.nlargest(3)
                
                # 高发星期分析
                weekday_counts = time_data['day_of_week'].value_counts().sort_index()
                weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
                
                print(f"⏰ 案件高发时段: {', '.join([f'{h}时({c}起)' for h, c in peak_hours.items()])}")
                
                return {
                    'peak_hours': peak_hours.to_dict(),
                    'weekday_distribution': {weekday_names[k]: v for k, v in weekday_counts.items()}
                }
    except Exception as e:
        print(f"⚠️ 时间模式分析失败: {e}")
    return None
